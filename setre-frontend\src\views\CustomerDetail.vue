<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCustomerStore } from '../store/customerStore'
import { useSaleStore } from '../store/saleStore'
import type { Customer } from '../types/customer'

const props = defineProps<{
  id?: string
  isNew?: boolean
}>()

const route = useRoute()
const router = useRouter()
const customerStore = useCustomerStore()
const saleStore = useSaleStore()

const isNewCustomer = computed(() => props.isNew || route.params.id === 'new')
const isLoading = ref(false)
const isSubmitting = ref(false)
const error = ref('')
const success = ref('')
const showSalesHistory = ref(false)

// Yeni müşteri için varsayılan değerler
const customer = ref<Customer>({
  id: '',
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  address: {
    street: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'Türkiye'
  },
  company: '',
  notes: '',
  totalPurchases: 0,
  lastPurchaseDate: '',
  status: 'active',
  customerType: 'regular',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
})

// Mevcut müşteriyi yükle
onMounted(async () => {
  try {
    isLoading.value = true
    error.value = ''

    // Yeni müşteri değilse, mevcut müşteriyi getir
    if (!isNewCustomer.value) {
      const customerId = route.params.id as string
      await customerStore.fetchCustomerById(customerId)

      if (customerStore.currentCustomer) {
        customer.value = { ...customerStore.currentCustomer }

        // Müşterinin satış geçmişini yükle
        await saleStore.fetchSalesByCustomerId(customerId)
      } else {
        error.value = 'Müşteri bulunamadı'
        router.push('/customers')
      }
    }
  } catch (err) {
    error.value = 'Müşteri yüklenirken bir hata oluştu'
    console.error(err)
  } finally {
    isLoading.value = false
  }
})

// Müşteriyi kaydet
const saveCustomer = async () => {
  if (!customer.value.firstName || !customer.value.lastName) {
    error.value = 'Lütfen ad ve soyad alanlarını doldurun'
    return
  }

  if (customer.value.email && !isValidEmail(customer.value.email)) {
    error.value = 'Lütfen geçerli bir e-posta adresi girin'
    return
  }

  try {
    isSubmitting.value = true
    error.value = ''

    if (isNewCustomer.value) {
      // Yeni müşteri oluştur
      await customerStore.createCustomer(customer.value)
      success.value = 'Müşteri başarıyla oluşturuldu'
    } else {
      // Mevcut müşteriyi güncelle
      await customerStore.updateCustomer(customer.value.id, customer.value)
      success.value = 'Müşteri başarıyla güncellendi'
    }

    // Kısa bir süre sonra müşteri listesine dön
    setTimeout(() => {
      router.push('/customers')
    }, 1500)
  } catch (err) {
    error.value = 'Müşteri kaydedilirken bir hata oluştu'
    console.error(err)
  } finally {
    isSubmitting.value = false
  }
}

// E-posta doğrulama
const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// İptal et ve listeye dön
const cancelEdit = () => {
  router.push('/customers')
}

// Müşteri durumu ve tipi için etiket ve renkler
const getStatusClass = (status: string) => {
  switch (status) {
    case 'active': return 'status-active'
    case 'inactive': return 'status-inactive'
    case 'lead': return 'status-lead'
    default: return 'status-active'
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'active': return 'Aktif'
    case 'inactive': return 'Pasif'
    case 'lead': return 'Potansiyel'
    default: return 'Bilinmeyen'
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'vip': return 'Vip'
    case 'regular': return 'Normal'
    case 'new': return 'Yeni'
    default: return 'Bilinmeyen'
  }
}

// Satış detaylarına git
const navigateToSaleDetails = (saleId: string) => {
  router.push(`/sales/${saleId}`)
}

// Yeni satış oluştur
const createNewSale = () => {
  router.push({
    path: '/sales/new',
    query: { customerId: customer.value.id }
  })
}
</script>

<template>
  <div class="customer-detail-container">
    <div class="customer-header">
      <h1>{{ isNewCustomer ? 'Yeni Müşteri' : 'Müşteri Detayları' }}</h1>
      <div class="header-actions">
        <button class="btn" @click="cancelEdit">
          <span class="pi pi-times"></span>
          İptal
        </button>
        <button class="btn btn-primary" @click="saveCustomer" :disabled="isSubmitting">
          <span class="pi pi-save"></span>
          {{ isSubmitting ? 'Kaydediliyor...' : 'Kaydet' }}
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <span class="pi pi-spin pi-spinner"></span>
      <p>Yükleniyor...</p>
    </div>

    <div v-else-if="error && !success" class="error-message">
      <span class="pi pi-exclamation-triangle"></span>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="success" class="success-message">
      <span class="pi pi-check-circle"></span>
      <p>{{ success }}</p>
    </div>

    <div v-else class="customer-form-container">
      <div class="customer-form-layout">
        <!-- Kişisel Bilgiler -->
        <div class="card">
          <h2>Kişisel Bilgiler</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="firstName">Ad</label>
              <input id="firstName" type="text" class="form-control" v-model="customer.firstName" placeholder="Müşteri adı" required />
            </div>

            <div class="form-group">
              <label for="lastName">Soyad</label>
              <input id="lastName" type="text" class="form-control" v-model="customer.lastName" placeholder="Müşteri soyadı" required />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="email">E-posta</label>
              <input id="email" type="email" class="form-control" v-model="customer.email" placeholder="<EMAIL>" />
            </div>

            <div class="form-group">
              <label for="phone">Telefon</label>
              <input id="phone" type="tel" class="form-control" v-model="customer.phone" placeholder="(*************" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="company">Şirket</label>
              <input id="company" type="text" class="form-control" v-model="customer.company" placeholder="Şirket adı (varsa)" />
            </div>
          </div>
        </div>

        <!-- Adres Bilgileri -->
        <div class="card">
          <h2>Adres Bilgileri</h2>

          <div class="form-group">
            <label for="street">Sokak Adresi</label>
            <input id="street" type="text" class="form-control" v-model="customer.address?.street" placeholder="Sokak adresi" />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="city">Şehir</label>
              <input id="city" type="text" class="form-control" v-model="customer.address?.city" placeholder="Şehir" />
            </div>

            <div class="form-group">
              <label for="state">İlçe</label>
              <input id="state" type="text" class="form-control" v-model="customer.address?.state" placeholder="İlçe" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="postalCode">Posta Kodu</label>
              <input id="postalCode" type="text" class="form-control" v-model="customer.address?.postalCode" placeholder="Posta kodu" />
            </div>

            <div class="form-group">
              <label for="country">Ülke</label>
              <input id="country" type="text" class="form-control" v-model="customer.address?.country" placeholder="Ülke" />
            </div>
          </div>
        </div>

        <!-- Ek Bilgiler -->
        <div class="card">
          <h2>Ek Bilgiler</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="status">Durum</label>
              <select id="status" class="form-control" v-model="customer.status">
                <option value="active">Aktif</option>
                <option value="inactive">Pasif</option>
                <option value="lead">Potansiyel</option>
              </select>
            </div>

            <div class="form-group">
              <label for="type">Müşteri Tipi</label>
              <select id="type" class="form-control" v-model="customer.customerType">
                <option value="vip">VIP</option>
                <option value="regular">Düzenli</option>
                <option value="new">Yeni</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="notes">Notlar</label>
            <textarea id="notes" class="form-control" v-model="customer.notes" rows="4" placeholder="Müşteri hakkında notlar..."></textarea>
          </div>
        </div>
      </div>

      <!-- Müşteri Özeti ve Satış Geçmişi -->
      <div class="customer-sidebar">
        <!-- Müşteri Özeti -->
        <div v-if="!isNewCustomer" class="card customer-summary">
          <h2>Müşteri Özeti</h2>

          <div class="summary-item">
            <span class="summary-label">Durum:</span>
            <span class="status-badge" :class="getStatusClass(customer.status)">
              {{ getStatusLabel(customer.status) }}
            </span>
          </div>

          <div class="summary-item">
            <span class="summary-label">Müşteri Tipi:</span>
            <span>{{ customer.customerType ? getTypeLabel(customer.customerType) : 'regular' }}</span>
          </div>

          <div class="summary-item">
            <span class="summary-label">Toplam Alışveriş:</span>
            <span class="summary-value">{{ customer.totalPurchases?.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</span>
          </div>

          <div class="summary-item" v-if="customer.lastPurchaseDate">
            <span class="summary-label">Son Alışveriş:</span>
            <span class="summary-value">{{ new Date(customer.lastPurchaseDate).toLocaleDateString('tr-TR') }}</span>
          </div>

          <div class="summary-item">
            <span class="summary-label">Kayıt Tarihi:</span>
            <span class="summary-value">{{ new Date(customer.createdAt).toLocaleDateString('tr-TR') }}</span>
          </div>

          <button v-if="!isNewCustomer" class="btn btn-primary btn-block" @click="createNewSale">
            <span class="pi pi-shopping-cart"></span>
            Yeni Satış Oluştur
          </button>
        </div>

        <!-- Satış Geçmişi -->
        <div v-if="!isNewCustomer" class="card">
          <div class="card-header-with-action">
            <h2>Satış Geçmişi</h2>
            <button class="btn-icon" @click="showSalesHistory = !showSalesHistory" :title="showSalesHistory ? 'Gizle' : 'Göster'">
              <span :class="showSalesHistory ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"></span>
            </button>
          </div>

          <div v-if="showSalesHistory">
            <div v-if="saleStore.loading" class="loading-container-small">
              <span class="pi pi-spin pi-spinner"></span>
              <p>Satışlar yükleniyor...</p>
            </div>

            <div v-else-if="saleStore.error" class="error-message-small">
              <span class="pi pi-exclamation-triangle"></span>
              <p>{{ saleStore.error }}</p>
            </div>

            <div v-else-if="saleStore.customerSales.length === 0" class="empty-state-small">
              <p>Henüz satış kaydı bulunmuyor</p>
            </div>

            <div v-else class="sales-history">
              <div v-for="sale in saleStore.customerSales" :key="sale.id" class="sale-history-item" @click="navigateToSaleDetails(sale.id)">
                <div class="sale-history-date">
                  {{ new Date(sale.saleDate).toLocaleDateString('tr-TR') }}
                </div>
                <div class="sale-history-amount">
                  {{ sale.finalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
                </div>
                <div class="sale-history-status">
                  <span class="status-badge-small" :class="{
                    'status-paid': sale.paymentStatus === 'paid',
                    'status-pending': sale.paymentStatus === 'pending',
                    'status-cancelled': sale.paymentStatus === 'cancelled'
                  }"></span>
                </div>
                <div class="sale-history-arrow">
                  <span class="pi pi-chevron-right"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.customer-detail-container {
  width: 100%;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.customer-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-message,
.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.customer-form-container {
  display: flex;
  gap: 1.5rem;
}

.customer-form-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.customer-sidebar {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.card-header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.card-header-with-action h2 {
  margin: 0;
  padding: 0;
  border: none;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.form-group label {
  font-size: 0.875rem;
  color: #6B7280;
}

.form-control {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* Müşteri Özeti Stilleri */
.customer-summary {
  position: sticky;
  top: 1.5rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
  margin-bottom: 1rem;
}

.summary-label {
  color: #6B7280;
  font-size: 0.875rem;
}

.summary-value {
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-inactive {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6B7280;
}

.status-lead {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.btn-block {
  width: 100%;
  margin-top: 1rem;
}

/* Satış Geçmişi Stilleri */
.loading-container-small,
.error-message-small,
.empty-state-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  gap: 0.5rem;
  text-align: center;
  color: #6B7280;
  font-size: 0.875rem;
}

.loading-container-small .pi,
.error-message-small .pi {
  font-size: 1.25rem;
}

.sales-history {
  max-height: 300px;
  overflow-y: auto;
}

.sale-history-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sale-history-item:hover {
  background-color: #F9FAFB;
}

.sale-history-item:last-child {
  border-bottom: none;
}

.sale-history-date {
  flex: 1;
  font-size: 0.875rem;
}

.sale-history-amount {
  font-weight: 500;
  margin-right: 0.75rem;
}

.sale-history-status {
  margin-right: 0.75rem;
}

.status-badge-small {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-paid {
  background-color: var(--success-color);
}

.status-pending {
  background-color: var(--accent-color);
}

.status-cancelled {
  background-color: var(--error-color);
}

.sale-history-arrow {
  color: #D1D5DB;
  font-size: 0.75rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--primary-color);
}

@media (max-width: 1024px) {
  .customer-form-container {
    flex-direction: column;
  }

  .customer-sidebar {
    width: 100%;
  }

  .customer-summary {
    position: static;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .form-group {
    width: 100%;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .btn {
    width: 100%;
  }

  .customer-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>