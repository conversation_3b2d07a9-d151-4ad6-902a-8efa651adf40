<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSaleStore } from '../store/saleStore'
import { useCustomerStore } from '../store/customerStore'
import type { SaleFilter } from '../types/sale'

const router = useRouter()
const saleStore = useSaleStore()
const customerStore = useCustomerStore()

const startDate = ref('')
const endDate = ref('')
const showDeleteConfirm = ref(false)
const saleToDelete = ref<string | null>(null)

// Yükleme işlemi
onMounted(async () => {
  await Promise.all([
    saleStore.fetchSales(),
    customerStore.fetchCustomers()
  ])
})

// Satış filtreleme
const handleDateRangeChange = () => {
  if (startDate.value && endDate.value) {
    saleStore.setFilter({
      startDate: startDate.value,
      endDate: endDate.value
    })
  }
}

const handleFilterChange = (filter: Partial<SaleFilter>) => {
  saleStore.setFilter(filter)
}

const resetFilters = () => {
  startDate.value = ''
  endDate.value = ''
  saleStore.resetFilter()
}

// Müşteri adını getir
const getCustomerName = (customerId: string) => {
  const customer = customerStore.customers.find(c => c.id === customerId)
  return customer ? `${customer.firstName} ${customer.lastName}` : 'Bilinmeyen Müşteri'
}

// Satış işlemleri
const navigateToSaleDetails = (saleId: string) => {
  router.push(`/sales/${saleId}`)
}

const navigateToNewSale = () => {
  router.push('/sales/new')
}

const confirmDeleteSale = (saleId: string) => {
  saleToDelete.value = saleId
  showDeleteConfirm.value = true
}

const deleteSale = async () => {
  if (saleToDelete.value) {
    try {
      await saleStore.deleteSale(saleToDelete.value)
      showDeleteConfirm.value = false
      saleToDelete.value = null
    } catch (error) {
      console.error('Satış silme hatası:', error)
    }
  }
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  saleToDelete.value = null
}

// Ödeme durumu ve yöntemi için etiket renkleri
const getPaymentStatusClass = (status: string) => {
  switch (status) {
    case 'paid': return 'status-paid'
    case 'pending': return 'status-pending'
    case 'cancelled': return 'status-cancelled'
    default: return 'status-pending'
  }
}

const getPaymentMethodLabel = (method: string) => {
  switch (method) {
    case 'cash': return 'Nakit'
    case 'credit_card': return 'Kredi Kartı'
    case 'bank_transfer': return 'Banka Transferi'
    case 'other': return 'Diğer'
    default: return 'Bilinmeyen'
  }
}

const getPaymentStatusLabel = (status: string) => {
  switch (status) {
    case 'paid': return 'Ödendi'
    case 'pending': return 'Beklemede'
    case 'cancelled': return 'İptal Edildi'
    default: return 'Bilinmeyen'
  }
}
</script>

<template>
  <div class="sales-container">
    <div class="sales-header">
      <h1>Satışlar</h1>
      <button class="btn btn-primary" @click="navigateToNewSale">
        <span class="pi pi-plus"></span>
        Yeni Satış
      </button>
    </div>

    <div class="card">
      <div class="filters-container">
        <div class="date-range">
          <div class="filter-group">
            <label>Başlangıç Tarihi:</label>
            <input 
              type="date" 
              class="form-control" 
              v-model="startDate"
              @change="handleDateRangeChange"
            />
          </div>
          <div class="filter-group">
            <label>Bitiş Tarihi:</label>
            <input 
              type="date" 
              class="form-control" 
              v-model="endDate"
              @change="handleDateRangeChange"
            />
          </div>
        </div>

        <div class="filters">
          <div class="filter-group">
            <label>Ödeme Durumu:</label>
            <select 
              class="form-control" 
              @change="e => handleFilterChange({ paymentStatus: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="paid">Ödendi</option>
              <option value="pending">Beklemede</option>
              <option value="cancelled">İptal Edildi</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Ödeme Yöntemi:</label>
            <select 
              class="form-control" 
              @change="e => handleFilterChange({ paymentMethod: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="cash">Nakit</option>
              <option value="credit_card">Kredi Kartı</option>
              <option value="bank_transfer">Banka Transferi</option>
              <option value="other">Diğer</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Sıralama:</label>
            <select 
              class="form-control" 
              @change="e => handleFilterChange({ sortBy: e.target.value })"
            >
              <option value="date">Tarih</option>
              <option value="amount">Tutar</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Yön:</label>
            <select 
              class="form-control" 
              @change="e => handleFilterChange({ sortOrder: e.target.value })"
            >
              <option value="desc">Azalan</option>
              <option value="asc">Artan</option>
            </select>
          </div>

          <button class="btn" @click="resetFilters">
            <span class="pi pi-filter-slash"></span>
            Filtreleri Temizle
          </button>
        </div>
      </div>

      <div v-if="saleStore.loading" class="loading-container">
        <span class="pi pi-spin pi-spinner"></span>
        <p>Satışlar yükleniyor...</p>
      </div>

      <div v-else-if="saleStore.error" class="error-container">
        <span class="pi pi-exclamation-triangle"></span>
        <p>{{ saleStore.error }}</p>
        <button class="btn btn-primary" @click="saleStore.fetchSales">
          Yeniden Dene
        </button>
      </div>

      <div v-else-if="saleStore.filteredSales.length === 0" class="empty-state">
        <span class="pi pi-shopping-cart"></span>
        <p>Satış bulunamadı</p>
        <button class="btn btn-primary" @click="navigateToNewSale">
          Yeni Satış Oluştur
        </button>
      </div>

      <div v-else class="sales-table-container">
        <table class="sales-table">
          <thead>
            <tr>
              <th>Satış No</th>
              <th>Tarih</th>
              <th>Müşteri</th>
              <th>Tutar</th>
              <th>Ödeme Yöntemi</th>
              <th>Durum</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="sale in saleStore.filteredSales" 
              :key="sale.id"
              @click="navigateToSaleDetails(sale.id)"
              class="sale-row"
            >
              <td class="sale-id">
                #{{ sale.id.substring(0, 8) }}
              </td>
              <td>
                {{ new Date(sale.saleDate).toLocaleDateString('tr-TR') }}
              </td>
              <td>
                {{ getCustomerName(sale.customerId) }}
              </td>
              <td class="sale-amount">
                {{ sale.finalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
              </td>
              <td>
                {{ getPaymentMethodLabel(sale.paymentMethod) }}
              </td>
              <td>
                <span class="status-badge" :class="getPaymentStatusClass(sale.paymentStatus)">
                  {{ getPaymentStatusLabel(sale.paymentStatus) }}
                </span>
              </td>
              <td class="actions-cell" @click.stop>
                <button class="btn-icon" title="Düzenle" @click.stop="navigateToSaleDetails(sale.id)">
                  <span class="pi pi-pencil"></span>
                </button>
                <button class="btn-icon" title="Sil" @click.stop="confirmDeleteSale(sale.id)">
                  <span class="pi pi-trash"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Silme Onay Modalı -->
    <div v-if="showDeleteConfirm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Satış Silme Onayı</h3>
          <button class="modal-close" @click="cancelDelete">
            <span class="pi pi-times"></span>
          </button>
        </div>
        <div class="modal-body">
          <p>Bu satışı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="cancelDelete">İptal</button>
          <button class="btn btn-danger" @click="deleteSale">Sil</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sales-container {
  width: 100%;
}

.sales-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sales-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.date-range {
  display: flex;
  gap: 1rem;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group label {
  font-size: 0.75rem;
  color: #6B7280;
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi,
.error-container .pi,
.empty-state .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-container .pi {
  color: var(--error-color);
}

.sales-table-container {
  overflow-x: auto;
}

.sales-table {
  width: 100%;
  border-collapse: collapse;
}

.sales-table th,
.sales-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.sales-table th {
  font-weight: 600;
  color: #6B7280;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sale-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sale-row:hover {
  background-color: #F9FAFB;
}

.sale-id {
  font-weight: 500;
}

.sale-amount {
  font-weight: 500;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-paid {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
}

.status-cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--text-color);
}

.btn-icon .pi-pencil:hover {
  color: var(--primary-color);
}

.btn-icon .pi-trash:hover {
  color: var(--error-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
}

@media (max-width: 1024px) {
  .date-range,
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .sales-table th:nth-child(5),
  .sales-table td:nth-child(5) {
    display: none;
  }
}

@media (max-width: 640px) {
  .sales-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .sales-header button {
    width: 100%;
  }
  
  .sales-table th:nth-child(3),
  .sales-table td:nth-child(3) {
    display: none;
  }
}
</style>