<?php

declare(strict_types=1);

use System\Migration\Migration;

class installment extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS installment (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `sale_id` INT NOT NULL,
         `amount` DECIMAL(10,2) NOT NULL,
         `payment` DECIMAL(10,2) DEFAULT 0,
         `status` VARCHAR(10) NOT NULL DEFAULT 'pending',
         `due_at` DATETIME NOT NULL,
         `paid_at` DATETIME DEFAULT NULL,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS installment");
   }
}
