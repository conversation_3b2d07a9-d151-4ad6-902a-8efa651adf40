<?php

declare(strict_types=1);

namespace App\Modules\Installment;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;

/**
 * @OA\Tag(name="Installment", description="Taksit işlemleri")
 */
class InstallmentController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected InstallmentService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Installment"}, path="/installment/", summary="Taksit listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllInstallment() {
      $this->response(function () {
         $result = $this->service->getAll();
         $result = array_map(function ($item) {
            $installment = new InstallmentResponse();
            $installment->fromRequest($item);
            return $installment;
         }, $result);
         return $result;
      });
   }

   /**
    * @OA\Get(tags={"Installment"}, path="/installment/{id}", summary="Taksit detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getInstallment(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getInstallment($id);
         $installment = new InstallmentResponse();
         $installment->fromRequest($result);
         return $installment;
      });
   }

   /**
    * @OA\Post(tags={"Installment"}, path="/installment/", summary="Taksit ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *    required={"sale_id", "amount", "payment", "status", "due_at", "paid_at"},
    *       @OA\Property(property="sale_id", type="integer", example=1),
    *       @OA\Property(property="amount", type="number", example=100),
    *       @OA\Property(property="payment", type="number", example=100),
    *       @OA\Property(property="status", type="string", example="pending"),
    *       @OA\Property(property="due_at", type="string", example="2026-01-01 00:00:01"),
    *       @OA\Property(property="paid_at", type="string", example="")
    *    ))
    * )
    */
   public function createInstallment() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new InstallmentRequest();
         $dto->fromRequest($request);
         $result = $this->service->createInstallment($dto);
         $installment = new InstallmentResponse();
         $installment->fromRequest($result);
         return $installment;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Installment"}, path="/installment/", summary="Taksit güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *    required={"id", "amount", "payment", "status", "due_at", "paid_at"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="amount", type="number", example=100),
    *       @OA\Property(property="payment", type="number", example=100),
    *       @OA\Property(property="status", type="string", example="pending"),
    *       @OA\Property(property="due_at", type="string", example="2026-01-01 00:00:01"),
    *       @OA\Property(property="paid_at", type="string", example="")
    *    ))
    * )
    */
   public function updateInstallment() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new InstallmentRequest();
         $dto->fromRequest($request);
         $result = $this->service->updateInstallment($dto);
         $installment = new InstallmentResponse();
         $installment->fromRequest($result);
         return $installment;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Installment"}, path="/installment/{id}", summary="Taksit sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteInstallment(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(
            ['id' => $id,
             'deleted_at' => ['IS NULL']
            ]);
         return $result;
      });
   }
}
