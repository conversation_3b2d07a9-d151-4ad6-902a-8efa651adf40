export interface ICategory extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content?: string;
   image_path: string;
   is_active: number;
   sort_order: number;
}

export interface ICategoryStore {
   id?: number;
   code: string;
   title: string;
   content?: string;
   image_path?: string;
   is_active?: number;
   sort_order?: number;
   image_upload?: File[];
}

export const useGetCategoryAll = (payload?: TQuery<ICategory[]>) => {
   const options = computed(() => ({
      queryKey: ["category", "categoryAll"],
      queryFn: async () => {
         return (await appAxios.get("/category/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCategoryById = (payload?: { id?: MaybeRef<string> } & TQuery<ICategory>) => {
   const options = computed(() => ({
      queryKey: ["category", "categoryById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/category/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "updateCategory"],
      mutationFn: async (data: ICategoryStore): Promise<TResponse<ICategory>> => {
         return (await appAxios.put("/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateCategory = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["category", "createCategory"],
      mutationFn: async (data: ICategoryStore): Promise<TResponse<ICategory>> => {
         return (await appAxios.post("/category/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["category"] });
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
