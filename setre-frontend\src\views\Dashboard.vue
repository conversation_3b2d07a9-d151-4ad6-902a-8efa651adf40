<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSaleStore } from '../store/saleStore'
import { useCustomerStore } from '../store/customerStore'
import { useProductStore } from '../store/productStore'

const router = useRouter()
const saleStore = useSaleStore()
const customerStore = useCustomerStore()
const productStore = useProductStore()

const dateRange = ref('today') // today, week, month, year
const isLoading = ref(true)

// Yükleme işlemi
onMounted(async () => {
  try {
    isLoading.value = true
    await Promise.all([
      saleStore.fetchSales(),
      saleStore.fetchSalesStatistics(),
      customerStore.fetchCustomers(),
      productStore.fetchProducts()
    ])
  } catch (error) {
    console.error('Dashboard veri yükleme hatası:', error)
  } finally {
    isLoading.value = false
  }
})

// Hesaplanan değerler
const totalSales = computed(() => saleStore.statistics?.totalSales || 0)
const totalRevenue = computed(() => saleStore.statistics?.totalRevenue || 0)
const averageSaleValue = computed(() => saleStore.statistics?.averageSaleValue || 0)
const totalCustomers = computed(() => customerStore.customers.length)
const totalProducts = computed(() => productStore.products.length)
const lowStockCount = computed(() => productStore.lowStockProducts.length)

// Tarih aralığını değiştir
const changeDateRange = (range: string) => {
  dateRange.value = range
  // Burada tarih aralığına göre verileri yeniden yükleyebilirsiniz
}

// Sayfa yönlendirmeleri
const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>Gösterge Paneli</h1>
      <div class="date-range-selector">
        <button 
          v-for="range in ['today', 'week', 'month', 'year']"
          :key="range"
          :class="['date-range-btn', { active: dateRange === range }]"
          @click="changeDateRange(range)"
        >
          {{ 
            range === 'today' ? 'Bugün' : 
            range === 'week' ? 'Bu Hafta' : 
            range === 'month' ? 'Bu Ay' : 'Bu Yıl' 
          }}
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <span class="pi pi-spin pi-spinner"></span>
      <p>Veriler yükleniyor...</p>
    </div>

    <div v-else class="dashboard-content">
      <!-- Özet Kartları -->
      <div class="summary-cards">
        <div class="card summary-card">
          <div class="card-icon sales-icon">
            <span class="pi pi-shopping-cart"></span>
          </div>
          <div class="card-content">
            <h3>Toplam Satış</h3>
            <p class="card-value">{{ totalSales }}</p>
            <p class="card-trend positive">+5% <span class="pi pi-arrow-up"></span></p>
          </div>
        </div>

        <div class="card summary-card">
          <div class="card-icon revenue-icon">
            <span class="pi pi-money-bill"></span>
          </div>
          <div class="card-content">
            <h3>Toplam Gelir</h3>
            <p class="card-value">{{ totalRevenue.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</p>
            <p class="card-trend positive">+8% <span class="pi pi-arrow-up"></span></p>
          </div>
        </div>

        <div class="card summary-card">
          <div class="card-icon customers-icon">
            <span class="pi pi-users"></span>
          </div>
          <div class="card-content">
            <h3>Toplam Müşteri</h3>
            <p class="card-value">{{ totalCustomers }}</p>
            <p class="card-trend positive">+3% <span class="pi pi-arrow-up"></span></p>
          </div>
        </div>

        <div class="card summary-card">
          <div class="card-icon products-icon">
            <span class="pi pi-box"></span>
          </div>
          <div class="card-content">
            <h3>Düşük Stok</h3>
            <p class="card-value">{{ lowStockCount }}</p>
            <p class="card-trend negative" v-if="lowStockCount > 0">Uyarı <span class="pi pi-exclamation-triangle"></span></p>
            <p class="card-trend positive" v-else>Stok Yeterli <span class="pi pi-check"></span></p>
          </div>
        </div>
      </div>

      <!-- Grafik ve Tablolar -->
      <div class="dashboard-charts">
        <div class="card chart-card sales-chart">
          <h3>Satış Grafiği</h3>
          <div class="chart-placeholder">
            <p>Satış grafiği burada görüntülenecek</p>
            <p class="chart-note">Not: Gerçek uygulamada Chart.js veya benzeri bir kütüphane kullanılabilir</p>
          </div>
        </div>

        <div class="card chart-card payment-methods">
          <h3>Ödeme Yöntemleri</h3>
          <div class="chart-placeholder">
            <p>Ödeme yöntemleri dağılımı burada görüntülenecek</p>
            <p class="chart-note">Not: Gerçek uygulamada Chart.js veya benzeri bir kütüphane kullanılabilir</p>
          </div>
        </div>
      </div>

      <!-- Son İşlemler ve Hızlı Erişim -->
      <div class="dashboard-bottom">
        <div class="card recent-sales">
          <div class="card-header">
            <h3>Son Satışlar</h3>
            <button class="btn btn-primary" @click="navigateTo('/sales')">
              Tümünü Gör
            </button>
          </div>
          
          <div class="recent-sales-list" v-if="saleStore.sales.length > 0">
            <div 
              v-for="sale in saleStore.sales.slice(0, 5)" 
              :key="sale.id"
              class="recent-sale-item"
              @click="navigateTo(`/sales/${sale.id}`)"
            >
              <div class="sale-info">
                <p class="sale-id">#{{ sale.id.substring(0, 8) }}</p>
                <p class="sale-customer">{{ sale.customer?.firstName || 'Müşteri' }} {{ sale.customer?.lastName || '' }}</p>
              </div>
              <div class="sale-details">
                <p class="sale-date">{{ new Date(sale.saleDate).toLocaleDateString('tr-TR') }}</p>
                <p class="sale-amount">{{ sale.finalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</p>
                <span 
                  class="sale-status" 
                  :class="{
                    'status-paid': sale.paymentStatus === 'paid',
                    'status-pending': sale.paymentStatus === 'pending',
                    'status-cancelled': sale.paymentStatus === 'cancelled'
                  }"
                >
                  {{ 
                    sale.paymentStatus === 'paid' ? 'Ödendi' : 
                    sale.paymentStatus === 'pending' ? 'Beklemede' : 'İptal Edildi' 
                  }}
                </span>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-state">
            <span class="pi pi-shopping-cart"></span>
            <p>Henüz satış kaydı bulunmuyor</p>
            <button class="btn btn-primary" @click="navigateTo('/sales/new')">
              Yeni Satış Oluştur
            </button>
          </div>
        </div>

        <div class="card quick-actions">
          <h3>Hızlı İşlemler</h3>
          <div class="quick-actions-grid">
            <button class="quick-action-btn" @click="navigateTo('/sales/new')">
              <span class="pi pi-plus"></span>
              <span>Yeni Satış</span>
            </button>
            <button class="quick-action-btn" @click="navigateTo('/customers/new')">
              <span class="pi pi-user-plus"></span>
              <span>Yeni Müşteri</span>
            </button>
            <button class="quick-action-btn" @click="navigateTo('/products')">
              <span class="pi pi-box"></span>
              <span>Ürünleri Yönet</span>
            </button>
            <button class="quick-action-btn" @click="navigateTo('/sales')">
              <span class="pi pi-list"></span>
              <span>Satışları Listele</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.date-range-selector {
  display: flex;
  gap: 0.5rem;
}

.date-range-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  background-color: white;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-range-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.loading-container .pi {
  font-size: 2rem;
  color: var(--primary-color);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.card-icon .pi {
  font-size: 1.5rem;
  color: white;
}

.sales-icon {
  background-color: var(--primary-color);
}

.revenue-icon {
  background-color: var(--success-color);
}

.customers-icon {
  background-color: var(--accent-color);
}

.products-icon {
  background-color: var(--error-color);
}

.card-content h3 {
  font-size: 0.875rem;
  color: #6B7280;
  margin: 0 0 0.5rem 0;
}

.card-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.card-trend {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0;
}

.card-trend.positive {
  color: var(--success-color);
}

.card-trend.negative {
  color: var(--error-color);
}

.dashboard-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-card {
  padding: 1.5rem;
}

.chart-card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.chart-placeholder {
  height: 250px;
  background-color: #F9FAFB;
  border-radius: 0.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6B7280;
}

.chart-note {
  font-size: 0.75rem;
  color: #9CA3AF;
  margin-top: 0.5rem;
}

.dashboard-bottom {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.recent-sales-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recent-sale-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 0.375rem;
  background-color: #F9FAFB;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-sale-item:hover {
  background-color: #F3F4F6;
}

.sale-id {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
}

.sale-customer {
  font-size: 0.75rem;
  color: #6B7280;
  margin: 0;
}

.sale-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sale-date {
  font-size: 0.75rem;
  color: #6B7280;
  margin: 0;
}

.sale-amount {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

.sale-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.status-paid {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
}

.status-cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6B7280;
  gap: 0.75rem;
}

.empty-state .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: #F9FAFB;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.5rem;
}

.quick-action-btn:hover {
  background-color: #F3F4F6;
  border-color: #D1D5DB;
}

.quick-action-btn .pi {
  font-size: 1.25rem;
  color: var(--primary-color);
}

@media (max-width: 1024px) {
  .dashboard-charts,
  .dashboard-bottom {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .date-range-selector {
    width: 100%;
    overflow-x: auto;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .sale-details {
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
  }
}
</style>