<?php

declare(strict_types=1);

return [
   'just'          => 'just',
   'seconds_ago'   => '%s second(s) ago',
   'minutes_ago'   => '%s minute(s) ago',
   'hours_ago'     => '%s hour(s) ago',
   'days_ago'      => '%s day(s) ago',
   'weeks_ago'     => '%s week(s) ago',
   'months_ago'    => '%s month(s) ago',
   'years_ago'     => '%s year(s) ago',
   'x_year'        => '%syear(s)',
   'x_month'       => '%smonth(s)',
   'x_week'        => '%sweek(s)',
   'x_day'         => '%sday(s)',
   'x_hour'        => '%shour(s)',
   'x_minute'      => '%sminute(s)',
   'x_second'      => '%ssecond(s)',
   'x_millisecond' => '%smillisecond(s)',
];
