import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Product, ProductFilter } from '../types/product'
import productService from '../services/productService'

export const useProductStore = defineStore('product', () => {
  // State
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filter = ref<ProductFilter>({
    search: '',
    isActive: true,
    sortBy: 'name',
    sortOrder: 'asc'
  })

  // Getters
  const filteredProducts = computed(() => {
    let result = [...products.value]
    
    // Arama filtresi
    if (filter.value.search) {
      const searchTerm = filter.value.search.toLowerCase()
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.sku.toLowerCase().includes(searchTerm) ||
        (product.barcode && product.barcode.includes(searchTerm)) ||
        (product.description && product.description.toLowerCase().includes(searchTerm))
      )
    }
    
    // Kategori filtresi
    if (filter.value.category) {
      result = result.filter(product => product.category === filter.value.category)
    }
    
    // Stok durumu filtresi
    if (filter.value.inStock !== undefined) {
      result = result.filter(product => 
        filter.value.inStock ? product.stockQuantity > 0 : product.stockQuantity <= 0
      )
    }
    
    // Aktif durumu filtresi
    if (filter.value.isActive !== undefined) {
      result = result.filter(product => product.isActive === filter.value.isActive)
    }
    
    // Fiyat aralığı filtresi
    if (filter.value.minPrice !== undefined) {
      result = result.filter(product => product.price >= filter.value.minPrice!)
    }
    
    if (filter.value.maxPrice !== undefined) {
      result = result.filter(product => product.price <= filter.value.maxPrice!)
    }
    
    // Sıralama
    if (filter.value.sortBy) {
      result.sort((a, b) => {
        let valueA, valueB
        
        if (filter.value.sortBy === 'name') {
          valueA = a.name.toLowerCase()
          valueB = b.name.toLowerCase()
        } else if (filter.value.sortBy === 'price') {
          valueA = a.price
          valueB = b.price
        } else if (filter.value.sortBy === 'stockQuantity') {
          valueA = a.stockQuantity
          valueB = b.stockQuantity
        } else {
          return 0
        }
        
        const direction = filter.value.sortOrder === 'desc' ? -1 : 1
        
        if (valueA < valueB) return -1 * direction
        if (valueA > valueB) return 1 * direction
        return 0
      })
    }
    
    return result
  })

  const lowStockProducts = computed(() => {
    return products.value.filter(product => 
      product.stockQuantity <= (product.minStockLevel || 5) && product.stockQuantity > 0
    )
  })

  const outOfStockProducts = computed(() => {
    return products.value.filter(product => product.stockQuantity <= 0)
  })

  // Actions
  const fetchProducts = async () => {
    loading.value = true
    error.value = null
    
    try {
      products.value = await productService.getAllProducts()
    } catch (err) {
      error.value = 'Ürünler yüklenirken bir hata oluştu.'
      console.error('Ürün yükleme hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchProductById = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      currentProduct.value = await productService.getProductById(id)
    } catch (err) {
      error.value = 'Ürün bilgileri yüklenirken bir hata oluştu.'
      console.error('Ürün detay hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const createProduct = async (product: Omit<Product, 'id'>) => {
    loading.value = true
    error.value = null
    
    try {
      const newProduct = await productService.createProduct(product)
      products.value.push(newProduct)
      return newProduct
    } catch (err) {
      error.value = 'Ürün oluşturulurken bir hata oluştu.'
      console.error('Ürün oluşturma hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProduct = async (id: string, product: Partial<Product>) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedProduct = await productService.updateProduct(id, product)
      
      // Ürün listesini güncelle
      const index = products.value.findIndex(p => p.id === id)
      if (index !== -1) {
        products.value[index] = updatedProduct
      }
      
      // Eğer mevcut ürün ise onu da güncelle
      if (currentProduct.value && currentProduct.value.id === id) {
        currentProduct.value = updatedProduct
      }
      
      return updatedProduct
    } catch (err) {
      error.value = 'Ürün güncellenirken bir hata oluştu.'
      console.error('Ürün güncelleme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteProduct = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      await productService.deleteProduct(id)
      
      // Ürünü listeden kaldır
      products.value = products.value.filter(p => p.id !== id)
      
      // Eğer mevcut ürün ise temizle
      if (currentProduct.value && currentProduct.value.id === id) {
        currentProduct.value = null
      }
    } catch (err) {
      error.value = 'Ürün silinirken bir hata oluştu.'
      console.error('Ürün silme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      return fetchProducts()
    }
    
    loading.value = true
    error.value = null
    
    try {
      products.value = await productService.searchProducts(query)
    } catch (err) {
      error.value = 'Ürün araması yapılırken bir hata oluştu.'
      console.error('Ürün arama hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchProductsByStockStatus = async (inStock: boolean) => {
    loading.value = true
    error.value = null
    
    try {
      products.value = await productService.getProductsByStockStatus(inStock)
    } catch (err) {
      error.value = 'Stok durumuna göre ürünler yüklenirken bir hata oluştu.'
      console.error('Stok durumu ürünleri hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const setFilter = (newFilter: Partial<ProductFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }

  const resetFilter = () => {
    filter.value = {
      search: '',
      isActive: true,
      sortBy: 'name',
      sortOrder: 'asc'
    }
  }

  return {
    // State
    products,
    currentProduct,
    loading,
    error,
    filter,
    
    // Getters
    filteredProducts,
    lowStockProducts,
    outOfStockProducts,
    
    // Actions
    fetchProducts,
    fetchProductById,
    createProduct,
    updateProduct,
    deleteProduct,
    searchProducts,
    fetchProductsByStockStatus,
    setFilter,
    resetFilter
  }
})