export interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address?: Address
  company?: string
  notes?: string
  totalPurchases?: number
  lastPurchaseDate?: string | null
  customerType?: 'regular' | 'vip' | 'new'
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}
export interface CustomerFilter {
  search?: string
  status?: 'active' | 'inactive' | 'all'
  customerType?: 'regular' | 'vip' | 'new' | 'all'
  sortBy?: 'name' | 'createdAt' | 'totalPurchases'
  sortOrder?: 'asc' | 'desc'
}

export interface Address {
  street?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
}
