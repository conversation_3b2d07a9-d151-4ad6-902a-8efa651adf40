<?php

declare(strict_types=1);

namespace App\Modules\Installment;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class InstallmentRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'installment'
   ) {
   }

   public function findAll(): array {
      return $this->database
         ->prepare('SELECT
               Installment.*
            FROM Installment
            WHERE Installment.deleted_at IS NULL
         ')
         ->execute()
         ->fetchAll();
   }

   public function findOne(int $id): array|false {
      return $this->database
         ->prepare('SELECT
               Installment.*
            FROM Installment
            WHERE Installment.deleted_at IS NULL
               AND Installment.id = :id
         ')
         ->execute([
            'id' => $id
         ])
         ->fetch();
   }

}
