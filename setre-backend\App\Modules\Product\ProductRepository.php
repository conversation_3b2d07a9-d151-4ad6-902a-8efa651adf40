<?php

declare(strict_types=1);

namespace App\Modules\Product;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'product'
   ) {
   }

   public function findCategory(int $product_id): array {
      return $this->database
         ->prepare('SELECT
               category.id,
               category.title
            FROM product_category

            JOIN category ON category.id = product_category.category_id
               AND category.deleted_at IS NULL
            WHERE product_category.product_id = :product_id
         ')
         ->execute([
            'product_id' => $product_id,
         ])
         ->fetchAll();
   }
}
