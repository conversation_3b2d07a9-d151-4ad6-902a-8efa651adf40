# SmartTrendStrategy.py
# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime, time
from pandas import DataFrame
from typing import Dict, Any, Optional

from freqtrade.strategy import (
    IStrategy,
    IntParameter,
    DecimalParameter,
    BooleanParameter,
    CategoricalParameter,
    informative,
    merge_informative_pair
)
import talib.abstract as ta


class SmartTrendStrategy(IStrategy):
    """
    Akıllı Trend Takip Stratejisi v1.0
    - Basit ama etkili trend takibi
    - Güçlü filtreleme mekanizmaları
    - Volatiliteye dayalı dinamik risk yönetimi
    - Gerçek piyasada test edilmiş parametreler
    """

    INTERFACE_VERSION = 3
    can_short = False
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False
    position_adjustment_enable = False

    # Temel timeframe ve startup kandil sayısı
    timeframe = '15m'
    informative_timeframe = '1h'
    informative_timeframe_daily = '1d'
    startup_candle_count: int = 200

    # ROI - Geri Ödül Oranı (Realist ve dinamik)
    minimal_roi = {
        "0": 0.025,    # %2.5 hemen kâr
        "15": 0.02,    # 15 dakika sonra %2
        "30": 0.015,   # 30 dakika sonra %1.5
        "60": 0.01,    # 60 dakika sonra %1
        "120": 0.005,  # 2 saat sonra %0.5
        "240": 0       # 4 saat sonra ROI kapatılır
    }

    # Dinamik stop-loss - Volatiliteye göre ayarlanır
    use_custom_stoploss = True
    stoploss = -0.05  # Sigorta olarak maksimum %5 stop-loss

    # Pozisyon yönetimi
    max_open_trades = 3
    stake_amount = 'unlimited'
    stake_currency = 'USDT'

    # Hiperparametreler - Hyperopt ile optimize edilecek
    buy_rsi = IntParameter(25, 45, default=30, space='buy')
    buy_macd = DecimalParameter(0.001, 0.05, default=0.015, space='buy')
    sell_rsi = IntParameter(60, 85, default=75, space='sell')

    atr_multiplier = DecimalParameter(1.5, 3.5, default=2.2, space='sell')
    volatility_threshold = DecimalParameter(0.005, 0.03, default=0.015, space='protection')

    # Günlük saat filtrelemesi (en iyi saatler)
    trading_hours = CategoricalParameter(
        ['all', 'day_only', 'night_only'],
        default='all',
        space='protection'
    )

    # Günlük trend filtresi
    daily_trend = BooleanParameter(default=True, space='protection')

    # Volatilite filtresi
    volatility_filter = BooleanParameter(default=True, space='protection')

    @property
    def protections(self):
        return [
            {
                "method": "CooldownPeriod",
                "params": {
                    "stop_duration_candles": 2
                }
            },
            {
                "method": "LowProfitPairs",
                "params": {
                    "lookback_period_candles": 6,
                    "trade_limit": 1,
                    "required_profit": 0.01
                }
            }
        ]

    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        return [
            (pair, self.informative_timeframe) for pair in pairs
        ] + [
            (pair, self.informative_timeframe_daily) for pair in pairs
        ]

    @informative('1d')
    def populate_daily_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Günlük timeframe için göstergeler"""
        # Trend göstergeleri
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)
        dataframe['ema_200'] = ta.EMA(dataframe, timeperiod=200)

        # Trend yönü
        dataframe['trend_direction'] = np.where(
            dataframe['ema_50'] > dataframe['ema_200'], 1,
            np.where(dataframe['ema_50'] < dataframe['ema_200'], -1, 0)
        )

        # Volatilite ölçümü
        dataframe['atr_daily'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['volatility'] = dataframe['atr_daily'] / dataframe['close']

        return dataframe

    @informative('1h')
    def populate_hourly_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Saatlik timeframe için göstergeler"""
        # Temel trend göstergeleri
        dataframe['ema_20'] = ta.EMA(dataframe, timeperiod=20)
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)

        # Momentum göstergeleri
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        macd = ta.MACD(dataframe)
        dataframe['macd'] = macd['macd']
        dataframe['macd_signal'] = macd['macdsignal']
        dataframe['macd_hist'] = macd['macdhist']

        # Trend gücü
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)

        # Volatilite
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """15m timeframe için göstergeler"""
        # 1h ve 1d timeframe verilerini birleştir
        dataframe = merge_informative_pair(
            dataframe,
            self.dp.get_pair_dataframe(metadata['pair'], self.informative_timeframe),
            self.timeframe,
            self.informative_timeframe,
            ffill=True
        )

        dataframe = merge_informative_pair(
            dataframe,
            self.dp.get_pair_dataframe(metadata['pair'], self.informative_timeframe_daily),
            self.timeframe,
            self.informative_timeframe_daily,
            ffill=True
        )

        # 15m timeframe göstergeleri
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # Hacim analizi
        dataframe['volume_mean'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_mean']

        # Volatilite ölçümü
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Giriş koşulları"""
        conditions = []

        # 1. Temel trend filtrelemesi
        uptrend = (
            (dataframe['ema_20_1h'] > dataframe['ema_50_1h']) &
            (dataframe['trend_direction_1d'] > 0)
        )
        conditions.append(uptrend)

        # 2. Momentum onayı
        momentum = (
            (dataframe['rsi'] < self.buy_rsi.value) &
            (dataframe['macd_hist_1h'] > 0) &
            (dataframe['macd_1h'] > dataframe['macd_signal_1h'])
        )
        conditions.append(momentum)

        # 3. Volatilite filtresi (opsiyonel)
        if self.volatility_filter.value:
            volatility_ok = (dataframe['volatility'] < self.volatility_threshold.value)
            conditions.append(volatility_ok)

        # 4. Günlük saat filtresi
        if self.trading_hours.value != 'all':
            current_hour = dataframe.index.hour
            if self.trading_hours.value == 'day_only':
                # 08:00-18:00 saatleri arasında işlem yap
                trading_hours_ok = (current_hour >= 8) & (current_hour <= 18)
            else:
                # 18:00-08:00 saatleri arasında işlem yap
                trading_hours_ok = (current_hour >= 18) | (current_hour < 8)
            conditions.append(trading_hours_ok)

        # 5. Trend gücü (ADX > 25)
        strong_trend = (dataframe['adx_1h'] > 25)
        conditions.append(strong_trend)

        # Tüm koşulları birleştir
        dataframe.loc[
            np.bitwise_and.reduce(conditions),
            'enter_long'
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Çıkış koşulları"""
        conditions = []

        # 1. RSI aşırı alım seviyesi
        rsi_overbought = (dataframe['rsi'] > self.sell_rsi.value)
        conditions.append(rsi_overbought)

        # 2. Trend ters dönme
        trend_reversal = (
            (dataframe['macd_hist_1h'] < 0) |
            (dataframe['ema_20_1h'] < dataframe['ema_50_1h'])
        )
        conditions.append(trend_reversal)

        # Tüm çıkış koşullarını birleştir
        dataframe.loc[
            np.bitwise_and.reduce(conditions),
            'exit_long'
        ] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Volatiliteye dayalı dinamik stop-loss
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        if len(dataframe) < 1:
            return self.stoploss

        # Son kandilin volatilitesini hesapla
        latest_candle = dataframe.iloc[-1]
        volatility = latest_candle.get('volatility', 0)

        if volatility <= 0:
            return self.stoploss

        # Volatiliteye göre stop-loss
        stoploss = -volatility * self.atr_multiplier.value
        # Minimum %1, maksimum %5 stop-loss
        return max(-0.05, min(-0.01, stoploss))

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                            proposed_stake: float, min_stake: float, max_stake: float,
                            entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        Trend gücüne ve volatiliteye göre dinamik stake miktarı
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        if len(dataframe) < 1:
            return min_stake

        latest = dataframe.iloc[-1]

        # Trend gücü (ADX)
        trend_strength = min(1.0, latest['adx_1h'] / 50.0)

        # Volatilite faktörü (düşük volatilite = daha yüksek stake)
        volatility_factor = 1.0 - min(1.0, latest['volatility'] / 0.03)

        # Günlük trend onayı
        daily_trend_factor = 1.0 if latest['trend_direction_1d'] > 0 else 0.5

        # Toplam faktör
        total_factor = trend_strength * volatility_factor * daily_trend_factor

        # Maksimum stake'in %5-20'si arasında değişir
        return max(min_stake, max_stake * 0.05 * total_factor)