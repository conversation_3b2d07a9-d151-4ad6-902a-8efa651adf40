export interface IProduct extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content?: string;
   is_active: number;
   sort_order: number;
   category_list: { id: number; title: string }[];
   image_list: { id: number; product_id: number; image_path: string }[];
}

export interface IProductStore {
   id?: number;
   code: string;
   title: string;
   content?: string;
   is_active?: number;
   sort_order?: number;
   product_category?: number[];
   image_path?: string[];
}

export const useGetProductAll = (payload?: TQuery<IProduct[]>) => {
   const options = computed(() => ({
      queryKey: ["product", "productAll"],
      queryFn: async () => {
         return (await appAxios.get("/product/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetProductById = (payload?: { id?: MaybeRef<string> } & TQuery<IProduct>) => {
   const options = computed(() => ({
      queryKey: ["product", "productById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/product/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "updateProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.put("/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "createProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.post("/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
