<?php

declare(strict_types=1);

use System\Migration\Migration;

class sale extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS sale (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `customer_id` INT NOT NULL,
         `product` VARCHAR(255) NOT NULL,
         `price` DECIMAL(10,2) NOT NULL,
         `installment` TINYINT NOT NULL,
         `installment_type` VARCHAR(10) NOT NULL DEFAULT 'monthly',
         `notes` VARCHAR(255) NOT NULL DEFAULT '',
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS sale");
   }
}
