<?php

declare(strict_types=1);

namespace App\Modules\Sale;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Sale\SaleRepository;

class SaleService extends BaseService {
   /** @var SaleRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      SaleRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getSale(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createSale(SaleRequest $dto): array {
      $customer = $this->repository->findBy(['id' => $dto->customer_id], 'customer');
      if (empty($customer)) {
         throw new SystemException('Customer not found', 404);
      }
      if($dto->installment < 1) {
         throw new SystemException('Installment must be greater than 0', 400);
      }
      if($dto->price < 0) {
         throw new SystemException('Price must be greater than 0', 400);
      }
      if(!in_array($dto->installment_type, ['monthly', 'weekly', 'daily'])) {
         throw new SystemException('Invalid installment type', 400);
      }

      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'customer_id'       => 'required',
            'product'           => 'required',
            'price'             => 'required|numeric',
            'installment'       => 'required|integer',
            'installment_type'  => 'required',
         ]);

         $id = $this->create([
            'customer_id' => $dto->customer_id,
            'product' => $dto->product,
            'price' => $dto->price,
            'installment' => $dto->installment,
            'installment_type' => $dto->installment_type,
            'notes' => $dto->notes,
         ]);

         return $this->getSale($id);
      });
   }

   public function updateSale(SaleRequest $dto): array {
      $customer = $this->repository->findBy(['id' => $dto->customer_id], 'customer');
      $sale = $this->repository->findOne($dto->id);
      if (empty($customer)) {
         throw new SystemException('Customer not found', 404);
      }
      if (empty($sale)) {
         throw new SystemException('Sale not found', 404);
      }
      if($dto->installment < 1) {
         throw new SystemException('Installment must be greater than 0', 400);
      }
      if($dto->price < 0) {
         throw new SystemException('Price must be greater than 0', 400);
      }
      if(!in_array($dto->installment_type, ['monthly', 'weekly', 'daily'])) {
         throw new SystemException('Invalid installment type', 400);
      }

      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'customer_id' => 'required',
            'product' => 'required',
            'price' => 'required|numeric',
            'installment' => 'required|integer',
            'installment_type' => 'required',
         ]);

         $this->update($dto, [
            'customer_id' => $dto->customer_id,
            'product' => $dto->product,
            'price' => $dto->price,
            'installment' => $dto->installment,
            'installment_type' => $dto->installment_type,
            'notes' => $dto->notes,
         ], [
            'id' => $dto->id
         ]);

         return $this->getSale($dto->id);
      });
   }

}
