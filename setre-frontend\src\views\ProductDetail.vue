<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductStore } from '../store/productStore'
import type { Product } from '../types/product'

const props = defineProps<{
  id?: string
  isNew?: boolean
}>()

const route = useRoute()
const router = useRouter()
const productStore = useProductStore()

const isNewProduct = computed(() => props.isNew || route.params.id === 'new')
const isLoading = ref(false)
const isSubmitting = ref(false)
const error = ref('')
const success = ref('')

// Yeni ürün için varsayılan değerler
const product = ref<Product>({
  id: '',
  name: '',
  description: '',
  sku: '',
  barcode: '',
  price: 0,
  costPrice: 0,
  stockQuantity: 0,
  category: '',
  imageUrl: '',
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  taxRate: 18, // Varsayılan KDV oranı
  unit: 'Adet',
  minStockLevel: 10,
  supplier: '',
  dimensions: {
    length: 0,
    width: 0,
    height: 0
  }
})

// Mevcut ürünü yükle
onMounted(async () => {
  try {
    isLoading.value = true
    error.value = ''

    // Yeni ürün değilse, mevcut ürünü getir
    if (!isNewProduct.value) {
      const productId = route.params.id as string
      await productStore.fetchProductById(productId)

      if (productStore.currentProduct) {
        product.value = { ...productStore.currentProduct }
      } else {
        error.value = 'Ürün bulunamadı'
        router.push('/products')
      }
    } else {
      // Yeni ürün için SKU oluştur
      product.value.sku = generateSku()
    }
  } catch (err) {
    error.value = 'Ürün yüklenirken bir hata oluştu'
    console.error(err)
  } finally {
    isLoading.value = false
  }
})

// Rastgele SKU oluştur
const generateSku = () => {
  const prefix = 'PRD'
  const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  const timestamp = Date.now().toString().slice(-4)
  return `${prefix}-${randomPart}-${timestamp}`
}

// Barkod oluştur
const generateBarcode = () => {
  const prefix = '978'
  const randomPart = Math.floor(Math.random() * 10000000000).toString().padStart(10, '0')
  product.value.barcode = `${prefix}${randomPart}`
}

// Ürünü kaydet
const saveProduct = async () => {
  if (!product.value.name) {
    error.value = 'Lütfen ürün adını girin'
    return
  }

  if (!product.value.price || product.value.price <= 0) {
    error.value = 'Lütfen geçerli bir fiyat girin'
    return
  }

  try {
    isSubmitting.value = true
    error.value = ''

    if (isNewProduct.value) {
      // Yeni ürün oluştur
      await productStore.createProduct(product.value)
      success.value = 'Ürün başarıyla oluşturuldu'
    } else {
      // Mevcut ürünü güncelle
      await productStore.updateProduct(product.value.id, product.value)
      success.value = 'Ürün başarıyla güncellendi'
    }

    // Kısa bir süre sonra ürün listesine dön
    setTimeout(() => {
      router.push('/products')
    }, 1500)
  } catch (err) {
    error.value = 'Ürün kaydedilirken bir hata oluştu'
    console.error(err)
  } finally {
    isSubmitting.value = false
  }
}

// İptal et ve listeye dön
const cancelEdit = () => {
  router.push('/products')
}

// Stok durumu için etiket ve renkler
const getStockStatusClass = computed(() => {
  const stockLevel = product.value.stockQuantity
  if (stockLevel <= 0) return 'status-out-of-stock'
  if (stockLevel < product.value.minStockLevel) return 'status-low-stock'
  return 'status-in-stock'
})

const getStockStatusLabel = computed(() => {
  const stockLevel = product.value.stockQuantity
  if (stockLevel <= 0) return 'Stokta Yok'
  if (stockLevel < product.value.minStockLevel) return 'Az Stok'
  return 'Stokta'
})

// Kategori etiketi
const getCategoryLabel = (category: string) => {
  switch (category) {
    case 'electronics': return 'Elektronik'
    case 'clothing': return 'Giyim'
    case 'home': return 'Ev Eşyaları'
    case 'food': return 'Gıda'
    case 'other': return 'Diğer'
    default: return 'Diğer'
  }
}
</script>

<template>
  <div class="product-detail-container">
    <div class="product-header">
      <h1>{{ isNewProduct ? 'Yeni Ürün' : 'Ürün Detayları' }}</h1>
      <div class="header-actions">
        <button class="btn" @click="cancelEdit">
          <span class="pi pi-times"></span>
          İptal
        </button>
        <button class="btn btn-primary" @click="saveProduct" :disabled="isSubmitting">
          <span class="pi pi-save"></span>
          {{ isSubmitting ? 'Kaydediliyor...' : 'Kaydet' }}
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <span class="pi pi-spin pi-spinner"></span>
      <p>Yükleniyor...</p>
    </div>

    <div v-else-if="error && !success" class="error-message">
      <span class="pi pi-exclamation-triangle"></span>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="success" class="success-message">
      <span class="pi pi-check-circle"></span>
      <p>{{ success }}</p>
    </div>

    <div v-else class="product-form-container">
      <div class="product-form-layout">
        <!-- Temel Bilgiler -->
        <div class="card">
          <h2>Temel Bilgiler</h2>

          <div class="form-group">
            <label for="name">Ürün Adı</label>
            <input id="name" type="text" class="form-control" v-model="product.name" placeholder="Ürün adını girin" required />
          </div>

          <div class="form-group">
            <label for="description">Açıklama</label>
            <textarea id="description" class="form-control" v-model="product.description" rows="4" placeholder="Ürün açıklamasını girin"></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="category">Kategori</label>
              <select id="category" class="form-control" v-model="product.category">
                <option value="electronics">Elektronik</option>
                <option value="clothing">Giyim</option>
                <option value="home">Ev Eşyaları</option>
                <option value="food">Gıda</option>
                <option value="other">Diğer</option>
              </select>
            </div>

            <div class="form-group">
              <label for="imageUrl">Resim URL</label>
              <input id="imageUrl" type="text" class="form-control" v-model="product.imageUrl" placeholder="Ürün resmi URL'si" />
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="product.isActive" />
              <span>Aktif</span>
            </label>
          </div>
        </div>

        <!-- Fiyat ve Stok -->
        <div class="card">
          <h2>Fiyat ve Stok</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="price">Satış Fiyatı</label>
              <div class="input-with-icon">
                <input id="price" type="number" class="form-control" v-model.number="product.price" min="0" step="0.01" required />
                <span class="input-icon">₺</span>
              </div>
            </div>

            <div class="form-group">
              <label for="costPrice">Maliyet Fiyatı</label>
              <div class="input-with-icon">
                <input id="costPrice" type="number" class="form-control" v-model.number="product.costPrice" min="0" step="0.01" />
                <span class="input-icon">₺</span>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="stockLevel">Stok Miktarı</label>
              <input id="stockLevel" type="number" class="form-control" v-model.number="product.stockQuantity" min="0" step="1" />
            </div>

            <div class="form-group">
              <label for="reorderLevel">Yeniden Sipariş Seviyesi</label>
              <input id="reorderLevel" type="number" class="form-control" v-model.number="product.minStockLevel" min="0" step="1" />
            </div>
          </div>

          <div class="stock-status">
            <span class="status-badge" :class="getStockStatusClass">
              {{ getStockStatusLabel }}
            </span>
          </div>
        </div>

        <!-- Ürün Detayları -->
        <div class="card">
          <h2>Ürün Detayları</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="sku">SKU</label>
              <div class="input-with-button">
                <input id="sku" type="text" class="form-control" v-model="product.sku" placeholder="Stok Kodu" readonly />
              </div>
            </div>

            <div class="form-group">
              <label for="barcode">Barkod</label>
              <div class="input-with-button">
                <input id="barcode" type="text" class="form-control" v-model="product.barcode" placeholder="Barkod" />
                <button class="btn-icon" @click="generateBarcode">
                  <span class="pi pi-refresh"></span>
                </button>
              </div>
            </div>
          </div>

          <h3>Boyutlar</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="length">Uzunluk (cm)</label>
              <input id="length" type="number" class="form-control" v-model.number="product.dimensions.length" min="0" step="0.1" />
            </div>

            <div class="form-group">
              <label for="width">Genişlik (cm)</label>
              <input id="width" type="number" class="form-control" v-model.number="product.dimensions.width" min="0" step="0.1" />
            </div>

            <div class="form-group">
              <label for="height">Yükseklik (cm)</label>
              <input id="height" type="number" class="form-control" v-model.number="product.dimensions.height" min="0" step="0.1" />
            </div>
          </div>
        </div>
      </div>

      <!-- Ürün Önizleme -->
      <div class="product-preview-container">
        <div class="card product-preview">
          <h2>Ürün Önizleme</h2>

          <div class="preview-image">
            <img v-if="product.imageUrl" :src="product.imageUrl" :alt="product.name" />
            <div v-else class="image-placeholder">
              <span class="pi pi-image"></span>
              <p>Resim Yok</p>
            </div>
          </div>

          <div class="preview-details">
            <h3>{{ product.name || 'Ürün Adı' }}</h3>

            <div class="preview-category" v-if="product.category">
              {{ getCategoryLabel(product.category) }}
            </div>

            <div class="preview-price">
              {{ product.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
            </div>

            <div class="preview-stock">
              <span class="status-badge" :class="getStockStatusClass">
                {{ getStockStatusLabel }}
              </span>
            </div>

            <div class="preview-description" v-if="product.description">
              {{ product.description }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-detail-container {
  width: 100%;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.product-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-message,
.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.product-form-container {
  display: flex;
  gap: 1.5rem;
}

.product-form-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.product-preview-container {
  width: 300px;
  flex-shrink: 0;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.card h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.form-group label {
  font-size: 0.875rem;
  color: #6B7280;
}

.form-control {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
}

.input-with-button {
  display: flex;
  align-items: center;
}

.input-with-button .form-control {
  flex: 1;
}

.input-with-button .btn-icon {
  margin-left: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-label input {
  margin: 0;
}

.stock-status {
  margin-top: 1rem;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-in-stock {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-low-stock {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
}

.status-out-of-stock {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--primary-color);
}

/* Ürün Önizleme Stilleri */
.product-preview {
  position: sticky;
  top: 1.5rem;
}

.preview-image {
  width: 100%;
  height: 200px;
  border-radius: 0.375rem;
  overflow: hidden;
  margin-bottom: 1rem;
  background-color: #F3F4F6;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9CA3AF;
}

.image-placeholder .pi {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.preview-details h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.preview-category {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 0.5rem;
}

.preview-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.preview-stock {
  margin-bottom: 1rem;
}

.preview-description {
  font-size: 0.875rem;
  color: #6B7280;
  line-height: 1.5;
}

@media (max-width: 1024px) {
  .product-form-container {
    flex-direction: column;
  }

  .product-preview-container {
    width: 100%;
  }

  .product-preview {
    position: static;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .form-group {
    width: 100%;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .btn {
    width: 100%;
  }

  .product-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>