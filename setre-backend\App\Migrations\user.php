<?php

declare(strict_types=1);

use System\Migration\Migration;

class user extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS user (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `username` <PERSON><PERSON><PERSON><PERSON>(55) NOT NULL,
         `name` <PERSON><PERSON><PERSON><PERSON>(55) NOT NULL,
         `surname` VA<PERSON><PERSON><PERSON>(55) NOT NULL,
         `email` VARCHAR(155) NULL,
         `password` VARCHAR(255) NOT NULL,
         `phone` VARCHAR(55) NULL,
         `role` VARCHAR(55) NOT NULL,
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS user");
   }
}
