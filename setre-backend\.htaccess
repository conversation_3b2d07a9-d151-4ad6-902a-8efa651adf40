<IfModule mod_rewrite.c>
   <IfModule mod_negotiation.c>
      Options -MultiViews
   </IfModule>

   RewriteEngine On

   # Protect dot files/folders
   RewriteCond %{REQUEST_URI} ^/\. [NC]
   RewriteCond %{REQUEST_URI} !^/\.well-known [NC]
   RewriteRule .* - [F,L]

   # Handle Authorization Header
   RewriteCond %{HTTP:Authorization} ^(.*)
   RewriteRule .* - [e=HTTP_AUTHORIZATION:%1]

   # RewriteCond %{REQUEST_FILENAME} !-d
   # RewriteRule ^(.*)/$ /$1 [L,R=301]

   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteRule ^(.+)$ index.php [QSA,L]
</IfModule>