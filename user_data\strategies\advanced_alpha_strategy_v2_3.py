# --- Revize Edilmiş ve Geliştirilmiş Strateji v2.3 ---
# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime
from pandas import DataFrame
from typing import Optional

from freqtrade.strategy import (
    IStrategy,
    Trade,
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    merge_informative_pair,
)

import talib.abstract as ta
from technical import qtpylib


class AdvancedAlphaStrategyV2_3(IStrategy):
    """
    Advanced Multi-Factor Alpha Strategy v2.3 - Konfigürasyon Uyumlu ve Tam Fonksiyonel
    - Spot piyasada çalışabilmesi için short işlemleri kapatıldı.
    - custom_stoploss özelliğinin aktif olarak kullanılması sağlandı.
    """

    INTERFACE_VERSION = 3

    # DÜZELTME: Spot piyasa konfigürasyonuyla uyumlu hale getirildi.
    can_short: bool = False

    # DÜZELTME: Dinamik stop-loss fonksiyonunu aktive et.
    use_custom_stoploss = True

    minimal_roi = {"0": 100}
    stoploss = -0.99 # Bu stop-loss'a asla gelinmemesi gerekir, sadece bir sigortadır.

    timeframe = '5m'
    informative_timeframe = '1h'

    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = True

    startup_candle_count: int = 200

    # Hiper-optimizasyon Parametreleri
    buy_rsi_threshold = IntParameter(20, 45, default=35, space="buy")
    sell_rsi_threshold = IntParameter(55, 80, default=65, space="sell")

    buy_alpha_score_threshold = DecimalParameter(0.1, 1.5, default=0.7, space="buy")

    buy_atr_multiplier = DecimalParameter(1.5, 4.0, default=2.5, space="buy")

    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        return [(pair, self.informative_timeframe) for pair in pairs]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=self.informative_timeframe)

        informative_df['ema_50'] = ta.EMA(informative_df, timeperiod=50)
        informative_df['ema_200'] = ta.EMA(informative_df, timeperiod=200)
        informative_df['adx'] = ta.ADX(informative_df, timeperiod=14)
        informative_df['regime'] = np.where(
            (informative_df['ema_50'] > informative_df['ema_200']) & (informative_df['adx'] > 25), 1,
            np.where((informative_df['ema_50'] < informative_df['ema_200']) & (informative_df['adx'] > 25), -1, 0)
        )
        dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, self.informative_timeframe, ffill=True)

        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=12)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=26)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        st = qtpylib.supertrend(dataframe, period=10, multiplier=3)
        dataframe['supertrend_direction'] = st['direction']

        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = qtpylib.safediv(dataframe['volume'], dataframe['volume_sma'], 1)

        rolling_window = 100

        def rolling_zscore(series):
            mean = series.rolling(rolling_window).mean()
            std = series.rolling(rolling_window).std()
            return qtpylib.safediv(series - mean, std, 0)

        rsi_norm = rolling_zscore(dataframe['rsi'])
        volume_norm = rolling_zscore(dataframe['volume_ratio'])

        atr_percent = (dataframe['atr'] / dataframe['close'])
        volatility_norm = rolling_zscore(atr_percent) * -1

        dataframe['alpha_score'] = rsi_norm + volume_norm + volatility_norm

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        long_conditions = (
            (dataframe['regime_1h'].ffill() >= 0) &
            (dataframe['supertrend_direction'] == 1) &
            (dataframe['volume'] > 0) &
            (dataframe['alpha_score'] > self.buy_alpha_score_threshold.value) &
            (dataframe['rsi'] < self.buy_rsi_threshold.value) &
            (qtpylib.crossed_above(dataframe['ema_fast'], dataframe['ema_slow']))
        )
        dataframe.loc[long_conditions, ['enter_long', 'enter_tag']] = (1, 'long_ema_cross')

        # Short koşulları can_short=False olduğu için Freqtrade tarafından görmezden gelinecektir.
        # Bu yüzden silmeye gerek yok, ileride lazım olabilir.

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        long_exit = (
            (qtpylib.crossed_below(dataframe['ema_fast'], dataframe['ema_slow'])) |
            (dataframe['rsi'] > self.sell_rsi_threshold.value)
        )
        dataframe.loc[long_exit, ['exit_long', 'exit_tag']] = (1, 'exit_long_signal')

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        latest_candle = dataframe.iloc[-1]

        atr_value = latest_candle.get('atr', 0)
        if atr_value == 0:
            return -self.stoploss # ATR değeri yoksa, sigorta stop-loss'u kullan

        stop_price = atr_value * self.buy_atr_multiplier.value
        stoploss_pct = (stop_price / current_rate)

        return -abs(max(0.02, stoploss_pct))