<?php

declare(strict_types=1);

namespace App\Modules\User;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;

/**
 * @OA\Tag(name="User", description="Kullanıcı işlemleri")
 */
class UserController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected UserService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"User"}, path="/user/", summary="Kullanıcı listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllUser() {
      $this->response(function () {
         $result = $this->service->getAllUser();
         $result = array_map(function ($item) {
            $user = new UserResponse();
            $user->fromRequest($item);
            return $user;
         }, $result);
         return $result;
      });
   }

   /**
    * @OA\Get(tags={"User"}, path="/user/{id}", summary="<PERSON><PERSON><PERSON><PERSON>ı detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getUser(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getUser($id);
         $user = new UserResponse();
         $user->fromRequest($result);
         return $user;
      });
   }

   /**
    * @OA\Post(tags={"User"}, path="/user/", summary="Kullanıcı ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"username", "name", "surname", "email", "phone", "password", "role"},
    *       @OA\Property(property="username", type="string", example="johndoe"),
    *       @OA\Property(property="name", type="string", example="John"),
    *       @OA\Property(property="surname", type="string", example="Doe"),
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *       @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="password", type="string", example="12345678"),
    *       @OA\Property(property="role", type="string", example="admin")
    *    ))
    * )
    */
   public function createUser() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new UserRequest();
         $dto->fromRequest($request);
         $result = $this->service->createUser($dto);
         $user = new UserResponse();
         $user->fromRequest($result);
         return $user;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"User"}, path="/user/", summary="Kullanıcı güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "username", "name", "surname", "email", "phone", "password", "role"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="username", type="string", example="johndoe"),
    *       @OA\Property(property="name", type="string", example="John"),
    *       @OA\Property(property="surname", type="string", example="Doe"),
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *             @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="password", type="string", example="12345678"),
    *       @OA\Property(property="role", type="string", example="admin")
    *    ))
    * )
    */
   public function updateUser() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new UserRequest();
         $dto->fromRequest($request);
         $result = $this->service->updateUser($dto);
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"User"}, path="/user/{id}", summary="Kullanıcı sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteUser(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(['id' => $id, 'deleted_at' => null]);
         return $result;
      });
   }
}
