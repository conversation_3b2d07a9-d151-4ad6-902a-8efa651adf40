<?php

declare(strict_types=1);

namespace App\Modules\Customer;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;

/**
 * @OA\Tag(name="Customer", description="Müşteri işlemleri")
 */
class CustomerController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected CustomerService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Customer"}, path="/customer/", summary="Müşteri listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllCustomer() {
      $this->response(function () {
         $result = $this->service->getAll();
         $result = array_map(function ($item) {
            $customer = new CustomerResponse();
            $customer->fromArray($item);
            return $customer;
         }, $result);
         return $result;
      });
   }

   /**
    * @OA\Get(tags={"Customer"}, path="/customer/{id}", summary="Müşteri detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getCustomer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getCustomer($id);
         $customer = new CustomerResponse();
         $customer->fromArray($result);
         return $customer;
      });
   }

   /**
    * @OA\Post(tags={"Customer"}, path="/customer/", summary="Müşteri ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"name", "surname", "phone"},
    *       @OA\Property(property="name", type="string", example="John"),
    *       @OA\Property(property="surname", type="string", example="Doe"),
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *       @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="tckn", type="string", example="12345678901"),
    *       @OA\Property(property="address", type="string", example="İstanbul"),
    *       @OA\Property(property="current_debit", type="number", example=100),
    *       @OA\Property(property="is_risky", type="integer", example="0"),
    *       @OA\Property(property="is_active", type="integer", example="1"),
    *       @OA\Property(property="notes", type="string", example="Notlar")
    *    ))
    * )
    */
   public function createCustomer() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new CustomerRequest();
         $dto->fromArray($request);
         $result = $this->service->createCustomer($dto);
         $customer = new CustomerResponse();
         $customer->fromArray($result);
         return $customer;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Customer"}, path="/customer/", summary="Müşteri güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "name", "surname", "phone"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="name", type="string", example="John"),
    *       @OA\Property(property="surname", type="string", example="Doe"),
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *       @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="tckn", type="string", example="12345678901"),
    *       @OA\Property(property="address", type="string", example="İstanbul"),
    *       @OA\Property(property="current_debit", type="number", example=100),
    *       @OA\Property(property="is_risky", type="integer", example="0"),
    *       @OA\Property(property="is_active", type="integer", example="1"),
    *       @OA\Property(property="notes", type="string", example="Notlar")
    *    ))
    * )
    */
   public function updateCustomer() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new CustomerRequest();
         $dto->fromArray($request);
         $result = $this->service->updateCustomer($dto);
         $customer = new CustomerResponse();
         $customer->fromArray($result);
         return $customer;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Customer"}, path="/customer/{id}", summary="Müşteri sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteCustomer(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(
            ['id' => $id,
             'deleted_at' => ['IS NULL']
            ]);
         return $result;
      });
   }
}
