import { saleApiClient } from './api'
import type { Sale, SaleCreate } from '../types/sale'
import customerService from './customerService'
import productService from './productService'

// Örnek satış verileri oluşturmak için yardımcı fonksiyon
const generateMockSales = async (): Promise<Sale[]> => {
  // Müşterileri al
  const customers = await customerService.getAllCustomers()
  // Ürünleri al
  const products = await productService.getAllProducts()
  
  // Eğer müşteri veya ürün yoksa boş dizi döndür
  if (customers.length === 0 || products.length === 0) {
    return []
  }
  
  // Örnek satışlar oluştur
  const mockSales: Sale[] = []
  
  for (let i = 0; i < 10; i++) {
    const customer = customers[Math.floor(Math.random() * customers.length)]
    const saleItems = []
    
    // Her satış için 1-3 ür<PERSON>n ekle
    const itemCount = Math.floor(Math.random() * 3) + 1
    
    for (let j = 0; j < itemCount; j++) {
      const product = products[Math.floor(Math.random() * products.length)]
      const quantity = Math.floor(Math.random() * 5) + 1
      const unitPrice = product.price
      const discount = Math.random() > 0.7 ? Math.floor(Math.random() * 10) : 0
      
      saleItems.push({
        id: `item-${i}-${j}`,
        productId: product.id,
        product: product,
        quantity: quantity,
        unitPrice: unitPrice,
        discount: discount,
        total: quantity * unitPrice * (1 - discount / 100)
      })
    }
    
    // Toplam tutarı hesapla
    const totalAmount = saleItems.reduce((sum, item) => sum + item.total, 0)
    const discount = Math.random() > 0.5 ? Math.floor(Math.random() * 5) : 0
    const tax = Math.floor(totalAmount * 0.18)
    const finalAmount = totalAmount - (totalAmount * discount / 100) + tax
    
    // Ödeme yöntemi ve durumu rastgele seç
    const paymentMethods = ['cash', 'credit_card', 'bank_transfer', 'other'] as const
    const paymentStatuses = ['paid', 'pending', 'cancelled'] as const
    
    const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)]
    const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)]
    
    // Satış tarihi son 30 gün içinde rastgele bir tarih
    const today = new Date()
    const saleDate = new Date(today.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
    
    mockSales.push({
      id: `sale-${i + 1}`,
      customerId: customer.id,
      customer: customer,
      items: saleItems,
      totalAmount: totalAmount,
      discount: discount,
      tax: tax,
      finalAmount: finalAmount,
      paymentMethod: paymentMethod,
      paymentStatus: paymentStatus,
      notes: Math.random() > 0.7 ? 'Müşteri notu' : undefined,
      saleDate: saleDate.toISOString(),
      createdAt: saleDate.toISOString(),
      updatedAt: saleDate.toISOString()
    })
  }
  
  return mockSales
}

// Satış verilerini saklayacak değişken
let mockSalesData: Sale[] | null = null

const saleService = {
  /**
   * Tüm satışları getirir
   */
  getAllSales: async (): Promise<Sale[]> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    return mockSalesData
  },

  /**
   * Belirli bir satışı ID'ye göre getirir
   */
  getSaleById: async (id: string): Promise<Sale> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    const sale = mockSalesData.find(s => s.id === id)
    if (!sale) {
      throw new Error('Satış bulunamadı')
    }
    
    return sale
  },

  /**
   * Yeni bir satış oluşturur
   */
  createSale: async (sale: SaleCreate): Promise<Sale> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    const newSale: Sale = {
      ...sale,
      id: `sale-${mockSalesData.length + 1}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    mockSalesData.push(newSale)
    return newSale
  },

  /**
   * Mevcut bir satışı günceller
   */
  updateSale: async (id: string, sale: Partial<Sale>): Promise<Sale> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    const index = mockSalesData.findIndex(s => s.id === id)
    if (index === -1) {
      throw new Error('Satış bulunamadı')
    }
    
    const updatedSale: Sale = {
      ...mockSalesData[index],
      ...sale,
      updatedAt: new Date().toISOString()
    }
    
    mockSalesData[index] = updatedSale
    return updatedSale
  },

  /**
   * Bir satışı siler
   */
  deleteSale: async (id: string): Promise<void> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    const index = mockSalesData.findIndex(s => s.id === id)
    if (index === -1) {
      throw new Error('Satış bulunamadı')
    }
    
    mockSalesData.splice(index, 1)
  },

  /**
   * Belirli bir müşterinin satışlarını getirir
   */
  getSalesByCustomerId: async (customerId: string): Promise<Sale[]> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    return mockSalesData.filter(sale => sale.customerId === customerId)
  },

  /**
   * Belirli bir tarih aralığındaki satışları getirir
   */
  getSalesByDateRange: async (startDate: string, endDate: string): Promise<Sale[]> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    const start = new Date(startDate).getTime()
    const end = new Date(endDate).getTime()
    
    return mockSalesData.filter(sale => {
      const saleTime = new Date(sale.saleDate).getTime()
      return saleTime >= start && saleTime <= end
    })
  },

  /**
   * Satış istatistiklerini getirir
   */
  getSalesStatistics: async (): Promise<any> => {
    // Eğer daha önce oluşturulmadıysa mock verileri oluştur
    if (!mockSalesData) {
      mockSalesData = await generateMockSales()
    }
    
    // Toplam satış sayısı
    const totalSales = mockSalesData.length
    
    // Toplam gelir
    const totalRevenue = mockSalesData.reduce((sum, sale) => sum + sale.finalAmount, 0)
    
    // Ortalama satış değeri
    const averageSaleValue = totalSales > 0 ? totalRevenue / totalSales : 0
    
    // Ödeme yöntemine göre satışlar
    const salesByPaymentMethod: Record<string, number> = {
      cash: 0,
      credit_card: 0,
      bank_transfer: 0,
      other: 0
    }
    
    mockSalesData.forEach(sale => {
      salesByPaymentMethod[sale.paymentMethod] += 1
    })
    
    // Tarihe göre satışlar (son 7 gün)
    const salesByDate: Record<string, number> = {}
    const today = new Date()
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      salesByDate[dateStr] = 0
    }
    
    mockSalesData.forEach(sale => {
      const saleDate = sale.saleDate.split('T')[0]
      if (salesByDate[saleDate] !== undefined) {
        salesByDate[saleDate] += 1
      }
    })
    
    return {
      totalSales,
      totalRevenue,
      averageSaleValue,
      salesByPaymentMethod,
      salesByDate
    }
  }
}

export default saleService