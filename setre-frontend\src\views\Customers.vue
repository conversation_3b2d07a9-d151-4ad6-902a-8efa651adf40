<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCustomerStore } from '../store/customerStore'
import type { CustomerFilter } from '../types/customer'

const router = useRouter()
const customerStore = useCustomerStore()

const searchQuery = ref('')
const selectedCustomers = ref<string[]>([])
const showDeleteConfirm = ref(false)
const customerToDelete = ref<string | null>(null)

// Yükleme işlemi
onMounted(async () => {
  await customerStore.fetchCustomers()
})

// Müşteri filtreleme
const handleSearch = () => {
  customerStore.setFilter({ search: searchQuery.value })
}

const handleFilterChange = (filter: Partial<CustomerFilter>) => {
  customerStore.setFilter(filter)
}

const resetFilters = () => {
  searchQuery.value = ''
  customerStore.resetFilter()
}

// Müşteri seçimi
const toggleSelectAll = (event: Event) => {
  const checked = (event.target as HTMLInputElement).checked

  if (checked) {
    selectedCustomers.value = customerStore.filteredCustomers.map(customer => customer.id)
  } else {
    selectedCustomers.value = []
  }
}

const toggleSelectCustomer = (customerId: string) => {
  const index = selectedCustomers.value.indexOf(customerId)

  if (index === -1) {
    selectedCustomers.value.push(customerId)
  } else {
    selectedCustomers.value.splice(index, 1)
  }
}

const isSelected = (customerId: string) => {
  return selectedCustomers.value.includes(customerId)
}

const allSelected = computed(() => {
  return customerStore.filteredCustomers.length > 0 &&
         selectedCustomers.value.length === customerStore.filteredCustomers.length
})

// Müşteri işlemleri
const navigateToCustomerDetails = (customerId: string) => {
  router.push(`/customers/${customerId}`)
}

const navigateToNewCustomer = () => {
  router.push('/customers/new')
}

const confirmDeleteCustomer = (customerId: string) => {
  customerToDelete.value = customerId
  showDeleteConfirm.value = true
}

const deleteCustomer = async () => {
  if (customerToDelete.value) {
    try {
      await customerStore.deleteCustomer(customerToDelete.value)
      showDeleteConfirm.value = false
      customerToDelete.value = null
    } catch (error) {
      console.error('Müşteri silme hatası:', error)
    }
  }
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  customerToDelete.value = null
}

// Müşteri tipi ve durumu için etiket renkleri
const getCustomerTypeClass = (type?: string) => {
  switch (type) {
    case 'vip': return 'type-vip'
    case 'regular': return 'type-regular'
    case 'new': return 'type-new'
    default: return 'type-regular'
  }
}

const getStatusClass = (status: string) => {
  return status === 'active' ? 'status-active' : 'status-inactive'
}
</script>

<template>
  <div class="customers-container">
    <div class="customers-header">
      <h1>Müşteriler</h1>
      <button class="btn btn-primary" @click="navigateToNewCustomer">
        <span class="pi pi-plus"></span>
        Yeni Müşteri
      </button>
    </div>

    <div class="card">
      <div class="filters-container">
        <div class="search-box">
          <span class="pi pi-search search-icon"></span>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Müşteri ara..."
            @input="handleSearch"
          />
        </div>

        <div class="filters">
          <div class="filter-group">
            <label>Durum:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ status: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="active">Aktif</option>
              <option value="inactive">Pasif</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Müşteri Tipi:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ customerType: e.target.value })"
            >
              <option value="all">Tümü</option>
              <option value="vip">VIP</option>
              <option value="regular">Düzenli</option>
              <option value="new">Yeni</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Sıralama:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ sortBy: e.target.value })"
            >
              <option value="name">İsim</option>
              <option value="createdAt">Kayıt Tarihi</option>
              <option value="totalPurchases">Toplam Alışveriş</option>
            </select>
          </div>

          <div class="filter-group">
            <label>Yön:</label>
            <select
              class="form-control"
              @change="e => handleFilterChange({ sortOrder: e.target.value })"
            >
              <option value="asc">Artan</option>
              <option value="desc">Azalan</option>
            </select>
          </div>

          <button class="btn" @click="resetFilters">
            <span class="pi pi-filter-slash"></span>
            Filtreleri Temizle
          </button>
        </div>
      </div>

      <div v-if="customerStore.loading" class="loading-container">
        <span class="pi pi-spin pi-spinner"></span>
        <p>Müşteriler yükleniyor...</p>
      </div>

      <div v-else-if="customerStore.error" class="error-container">
        <span class="pi pi-exclamation-triangle"></span>
        <p>{{ customerStore.error }}</p>
        <button class="btn btn-primary" @click="customerStore.fetchCustomers">
          Yeniden Dene
        </button>
      </div>

      <div v-else-if="customerStore.filteredCustomers.length === 0" class="empty-state">
        <span class="pi pi-users"></span>
        <p>Müşteri bulunamadı</p>
        <button class="btn btn-primary" @click="navigateToNewCustomer">
          Yeni Müşteri Ekle
        </button>
      </div>

      <div v-else class="customers-table-container">
        <table class="customers-table">
          <thead>
            <tr>
              <th class="checkbox-cell">
                <input
                  type="checkbox"
                  :checked="allSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>Müşteri</th>
              <th>İletişim</th>
              <th>Durum</th>
              <th>Müşteri Tipi</th>
              <th>Son Alışveriş</th>
              <th>Toplam Alışveriş</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="customer in customerStore.filteredCustomers"
              :key="customer.id"
              @click="navigateToCustomerDetails(customer.id)"
              class="customer-row"
            >
              <td class="checkbox-cell" @click.stop>
                <input
                  type="checkbox"
                  :checked="isSelected(customer.id)"
                  @change="() => toggleSelectCustomer(customer.id)"
                />
              </td>
              <td class="customer-info">
                <div class="customer-name">
                  {{ customer.firstName }} {{ customer.lastName }}
                </div>
                <div class="customer-id">#{{ customer.id.substring(0, 8) }}</div>
              </td>
              <td>
                <div class="contact-info">
                  <div class="contact-email">
                    <span class="pi pi-envelope"></span>
                    {{ customer.email }}
                  </div>
                  <div class="contact-phone">
                    <span class="pi pi-phone"></span>
                    {{ customer.phone }}
                  </div>
                </div>
              </td>
              <td>
                <span class="status-badge" :class="getStatusClass(customer.status)">
                  {{ customer.status === 'active' ? 'Aktif' : 'Pasif' }}
                </span>
              </td>
              <td>
                <span class="type-badge" :class="getCustomerTypeClass(customer.customerType)">
                  {{
                    customer.customerType === 'vip' ? 'VIP' :
                    customer.customerType === 'regular' ? 'Düzenli' :
                    customer.customerType === 'new' ? 'Yeni' : 'Düzenli'
                  }}
                </span>
              </td>
              <td>
                {{ customer.lastPurchaseDate ? new Date(customer.lastPurchaseDate).toLocaleDateString('tr-TR') : '-' }}
              </td>
              <td>
                {{ customer.totalPurchases?.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) || 0 }} Adet
              </td>
              <td class="actions-cell" @click.stop>
                <button class="btn-icon" title="Düzenle" @click.stop="navigateToCustomerDetails(customer.id)">
                  <span class="pi pi-pencil"></span>
                </button>
                <button class="btn-icon" title="Sil" @click.stop="confirmDeleteCustomer(customer.id)">
                  <span class="pi pi-trash"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Silme Onay Modalı -->
    <div v-if="showDeleteConfirm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Müşteri Silme Onayı</h3>
          <button class="modal-close" @click="cancelDelete">
            <span class="pi pi-times"></span>
          </button>
        </div>
        <div class="modal-body">
          <p>Bu müşteriyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="cancelDelete">İptal</button>
          <button class="btn btn-danger" @click="deleteCustomer">Sil</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.customers-container {
  width: 100%;
}

.customers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.customers-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
}

.search-box input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group label {
  font-size: 0.75rem;
  color: #6B7280;
}

.loading-container,
.error-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi,
.error-container .pi,
.empty-state .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-container .pi {
  color: var(--error-color);
}

.customers-table-container {
  overflow-x: auto;
}

.customers-table {
  width: 100%;
  border-collapse: collapse;
}

.customers-table th,
.customers-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.customers-table th {
  font-weight: 600;
  color: #6B7280;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.checkbox-cell {
  width: 40px;
}

.customer-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.customer-row:hover {
  background-color: #F9FAFB;
}

.customer-info {
  display: flex;
  flex-direction: column;
}

.customer-name {
  font-weight: 500;
}

.customer-id {
  font-size: 0.75rem;
  color: #6B7280;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.contact-email,
.contact-phone {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-email .pi,
.contact-phone .pi {
  color: #6B7280;
  font-size: 0.75rem;
}

.status-badge,
.type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-inactive {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6B7280;
}

.type-vip {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
}

.type-regular {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
}

.type-new {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--text-color);
}

.btn-icon .pi-pencil:hover {
  color: var(--primary-color);
}

.btn-icon .pi-trash:hover {
  color: var(--error-color);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
}

@media (max-width: 1024px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .customers-table th:nth-child(5),
  .customers-table td:nth-child(5),
  .customers-table th:nth-child(6),
  .customers-table td:nth-child(6) {
    display: none;
  }
}

@media (max-width: 640px) {
  .customers-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .customers-header button {
    width: 100%;
  }

  .customers-table th:nth-child(4),
  .customers-table td:nth-child(4),
  .customers-table th:nth-child(7),
  .customers-table td:nth-child(7) {
    display: none;
  }
}
</style>