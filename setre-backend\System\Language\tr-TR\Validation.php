<?php

declare(strict_types=1);

return [
   'err_required'      => '%s alanı boş bırakılamaz',
   'err_numeric'       => '%s alanı sayısal bir değer olmalıdır',
   'err_email'         => 'E-posta adresi geçersiz',
   'err_min_len'       => '%s alanı en az %s karakter olmalıdır',
   'err_max_len'       => '%s alanı en fazla %s karakter olmalıdır',
   'err_exact_len'     => '%s alanı %s olmalıdır',
   'err_alpha'         => '%s alanı sadece harf içerebilir',
   'err_alpha_num'     => '%s alanı harf ve sayı içerebilir',
   'err_alpha_dash'    => '%s alanı harf, sayı ve altçizgi içerebilir',
   'err_alpha_space'   => '%s alanı harf, sayı ve boşluk içerebilir',
   'err_integer'       => '%s alanı tam sayısal bir değer olmalıdır',
   'err_boolean'       => '%s alanı boolean bir değer olmalıdır',
   'err_float'         => '%s alanı ondalık sayısal bir değer olmalıdır',
   'err_valid_url'     => '%s alanı geçerli bir URL olmalıdır',
   'err_valid_ip'      => '%s alanı geçerli bir IP olmalıdır',
   'err_valid_ipv4'    => '%s alanı geçerli bir IPv4 olmalıdır',
   'err_valid_ipv6'    => '%s alanı geçerli bir IPv6 olmalıdır',
   'err_valid_cc'      => '%s alanı geçerli bir kredi kartı numarası olmalıdır',
   'err_contains'      => '%s alanı "%s" içermelidir',
   'err_min_numeric'   => '%s alanı en az "%s" değeri alabilir',
   'err_max_numeric'   => '%s alanı en fazla "%s" değeri alabilir',
   'err_matches'       => '%s alanı %s alanı ile eşleşmiyor',
   'err_must_be_array' => '%s alanı bir dizi olmalıdır'
];
