#skeleton
from typing import Optional, Union
from pandas import DataFrame
from freqtrade.strategy import (IStrategy)

class skeleton(IStrategy):
    INTERFACE_VERSION = 3
    timeframe = '5m'
    use_exit_signal = False

    stoploss = -0.10
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.05

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
            #
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe.loc[
            (
                (dataframe['volume'] > 0)  # Make sure Volume is not 0
            ),
            'enter_long'] = 1
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe.loc[
            (
                #exit signals
            ),
            'exit_long'] = 1
        return dataframe
    