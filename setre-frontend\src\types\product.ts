export interface Product {
  id: string
  name: string
  description?: string
  sku: string
  barcode?: string
  price: number
  costPrice?: number
  stockQuantity: number
  category: string
  imageUrl?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  taxRate: number
  unit: string
  minStockLevel: number
  supplier: string
  dimensions: {
    length: number
    width: number
    height: number
  }
}

export interface ProductFilter {
  search?: string
  category?: string
  inStock?: boolean
  isActive?: boolean
  minPrice?: number
  maxPrice?: number
  sortBy?: 'name' | 'price' | 'stockQuantity'
  sortOrder?: 'asc' | 'desc'
}