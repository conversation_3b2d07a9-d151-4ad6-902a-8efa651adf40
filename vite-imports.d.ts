/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EButton: typeof import('./src/utils/enums')['EButton']
  const ELanguage: typeof import('./src/utils/enums')['ELanguage']
  const ENotify: typeof import('./src/utils/enums')['ENotify']
  const EUser: typeof import('./src/utils/enums')['EUser']
  const EffectScope: typeof import('vue')['EffectScope']
  const TDialog: typeof import('./src/utils/types')['TDialog']
  const VITE_MEDIA: typeof import('./src/utils/helper')['VITE_MEDIA']
  const VueQueryPlugin: typeof import('@tanstack/vue-query')['VueQueryPlugin']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const appAxios: typeof import('./src/utils/axios')['appAxios']
  const appConfig: typeof import('./src/utils/config')['appConfig']
  const appMenu: typeof import('./src/utils/menu')['appMenu']
  const appRules: typeof import('./src/utils/rules')['appRules']
  const axios: typeof import('axios')['default']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createFormData: typeof import('./src/utils/helper')['createFormData']
  const createI18n: typeof import('vue-i18n')['createI18n']
  const createPinia: typeof import('pinia')['createPinia']
  const createRouter: typeof import('vue-router')['createRouter']
  const createVuetify: typeof import('vuetify')['createVuetify']
  const createWebHistory: typeof import('vue-router')['createWebHistory']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const dragHide: typeof import('./src/utils/helper')['dragHide']
  const effectScope: typeof import('vue')['effectScope']
  const escapeUrl: typeof import('./src/utils/helper')['escapeUrl']
  const fetchLocales: typeof import('./src/utils/helper')['fetchLocales']
  const formatCounter: typeof import('./src/utils/helper')['formatCounter']
  const formatDate: typeof import('./src/utils/helper')['formatDate']
  const formatDecimal: typeof import('./src/utils/helper')['formatDecimal']
  const formatFraction: typeof import('./src/utils/helper')['formatFraction']
  const formatMS: typeof import('./src/utils/helper')['formatMS']
  const formatMoney: typeof import('./src/utils/helper')['formatMoney']
  const formatOklch: typeof import('./src/utils/helper')['formatOklch']
  const formatPadding: typeof import('./src/utils/helper')['formatPadding']
  const formatSize: typeof import('./src/utils/helper')['formatSize']
  const formatSlug: typeof import('./src/utils/helper')['formatSlug']
  const formatString: typeof import('./src/utils/helper')['formatString']
  const generateRandomGuid: typeof import('./src/utils/helper')['generateRandomGuid']
  const generateRandomString: typeof import('./src/utils/helper')['generateRandomString']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getComponent: typeof import('./src/utils/helper')['getComponent']
  const getComponentAsync: typeof import('./src/utils/helper')['getComponentAsync']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getCurrentWatcher: typeof import('vue')['getCurrentWatcher']
  const getFileExtension: typeof import('./src/utils/helper')['getFileExtension']
  const getProvider: typeof import('./src/utils/helper')['getProvider']
  const getUserLocale: typeof import('./src/utils/helper')['getUserLocale']
  const getUserTheme: typeof import('./src/utils/helper')['getUserTheme']
  const h: typeof import('vue')['h']
  const i18n: typeof import('./src/utils/i18n')['i18n']
  const imageFormData: typeof import('./src/utils/helper')['imageFormData']
  const inject: typeof import('vue')['inject']
  const inputFilter: typeof import('./src/utils/helper')['inputFilter']
  const inputRegex: typeof import('./src/utils/helper')['inputRegex']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const loadLocales: typeof import('./src/utils/helper')['loadLocales']
  const loadMenu: typeof import('./src/utils/helper')['loadMenu']
  const loadRoutes: typeof import('./src/utils/helper')['loadRoutes']
  const lowerCase: typeof import('./src/utils/helper')['lowerCase']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const mat: typeof import('./src/utils/icons-material')['mat']
  const matAliases: typeof import('./src/utils/icons-material')['matAliases']
  const mergeProps: typeof import('vue')['mergeProps']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const persistedstate: typeof import('pinia-plugin-persistedstate')['default']
  const pho: typeof import('./src/utils/icons-phosphor')['pho']
  const phoAliases: typeof import('./src/utils/icons-phosphor')['phoAliases']
  const pinia: typeof import('./src/utils/pinia')['pinia']
  const position: typeof import('./src/utils/helper')['position']
  const provide: typeof import('vue')['provide']
  const query: typeof import('./src/utils/query')['query']
  const queryData: typeof import('./src/utils/helper')['queryData']
  const queryOptions: typeof import('./src/utils/query')['queryOptions']
  const queryPrefetch: typeof import('./src/utils/helper')['queryPrefetch']
  const queryWrapper: typeof import('./src/utils/helper')['queryWrapper']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const registerDirectives: typeof import('./src/utils/directive')['registerDirectives']
  const registerI18n: typeof import('./src/utils/i18n')['registerI18n']
  const registerMenu: typeof import('./src/utils/menu')['registerMenu']
  const registerProviders: typeof import('./src/utils/provider')['registerProviders']
  const registerRoutes: typeof import('./src/utils/router')['registerRoutes']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const router: typeof import('./src/utils/router')['router']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setInitialData: typeof import('./src/utils/helper')['setInitialData']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const setProvider: typeof import('./src/utils/helper')['setProvider']
  const setUserLocale: typeof import('./src/utils/helper')['setUserLocale']
  const setUserTheme: typeof import('./src/utils/helper')['setUserTheme']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const tabler: typeof import('./src/utils/icons-tabler')['tabler']
  const tablerAliases: typeof import('./src/utils/icons-tabler')['tablerAliases']
  const tailwind3: typeof import('./src/utils/tailwind3')['default']
  const timerAttempt: typeof import('./src/utils/helper')['timerAttempt']
  const timerDebounce: typeof import('./src/utils/helper')['timerDebounce']
  const timerSleep: typeof import('./src/utils/helper')['timerSleep']
  const timerThrottle: typeof import('./src/utils/helper')['timerThrottle']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const ucFirst: typeof import('./src/utils/helper')['ucFirst']
  const ucWords: typeof import('./src/utils/helper')['ucWords']
  const unref: typeof import('vue')['unref']
  const upperCase: typeof import('./src/utils/helper')['upperCase']
  const useAppStore: typeof import('./src/stores/appStore')['useAppStore']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAuthStore: typeof import('./src/stores/authStore')['useAuthStore']
  const useConfirmStore: typeof import('./src/stores/confirmStore')['useConfirmStore']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useI18n: typeof import('vue-i18n')['useI18n']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useMessageStore: typeof import('./src/stores/messageStore')['useMessageStore']
  const useModel: typeof import('vue')['useModel']
  const useMutation: typeof import('@tanstack/vue-query')['useMutation']
  const useNotifyStore: typeof import('./src/stores/notifyStore')['useNotifyStore']
  const usePromptStore: typeof import('./src/stores/promptStore')['usePromptStore']
  const useQuery: typeof import('@tanstack/vue-query')['useQuery']
  const useQueryClient: typeof import('@tanstack/vue-query')['useQueryClient']
  const useReactiveStore: typeof import('./src/stores/reactiveStore')['useReactiveStore']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useSnackbarStore: typeof import('./src/stores/messageStore')['useSnackbarStore']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTheme: typeof import('vuetify')['useTheme']
  const useTranslate: typeof import('./src/utils/helper')['useTranslate']
  const vuetify: typeof import('./src/utils/vuetify')['vuetify']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, ShallowRef, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef, App } from 'vue'
  import('vue')
  // @ts-ignore
  export type { RouteLocation, RouteRecordRaw, Router, RouteMeta, RouteLocationNormalizedLoaded } from 'vue-router'
  import('vue-router')
  // @ts-ignore
  export type { VueQueryPluginOptions, UseQueryReturnType, UseQueryOptions } from '@tanstack/vue-query'
  import('@tanstack/vue-query')
  // @ts-ignore
  export type { ThemeInstance, IconProps } from 'vuetify'
  import('vuetify')
  // @ts-ignore
  export type { TResponse } from './src/utils/axios'
  import('./src/utils/axios')
  // @ts-ignore
  export type { EButton, ENotify, EUser, ELanguage } from './src/utils/enums'
  import('./src/utils/enums')
  // @ts-ignore
  export type { TQuery, TMutation } from './src/utils/query'
  import('./src/utils/query')
  // @ts-ignore
  export type { TRoute } from './src/utils/router'
  import('./src/utils/router')
  // @ts-ignore
  export type { IDefaultFields, ITranslate, THeader, TNotify, TSnackbar } from './src/utils/types'
  import('./src/utils/types')
  // @ts-ignore
  export type { TList, TDataTable, TMultiSelect, TDateField, TToolbar, TCard, TContainer, TBtn } from './src/utils/vuetify'
  import('./src/utils/vuetify')
}

// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly EButton: UnwrapRef<typeof import('./src/utils/enums')['EButton']>
    readonly ELanguage: UnwrapRef<typeof import('./src/utils/enums')['ELanguage']>
    readonly ENotify: UnwrapRef<typeof import('./src/utils/enums')['ENotify']>
    readonly EUser: UnwrapRef<typeof import('./src/utils/enums')['EUser']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly TDialog: UnwrapRef<typeof import('./src/utils/types')['TDialog']>
    readonly VITE_MEDIA: UnwrapRef<typeof import('./src/utils/helper')['VITE_MEDIA']>
    readonly VueQueryPlugin: UnwrapRef<typeof import('@tanstack/vue-query')['VueQueryPlugin']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('pinia')['acceptHMRUpdate']>
    readonly appAxios: UnwrapRef<typeof import('./src/utils/axios')['appAxios']>
    readonly appConfig: UnwrapRef<typeof import('./src/utils/config')['appConfig']>
    readonly appMenu: UnwrapRef<typeof import('./src/utils/menu')['appMenu']>
    readonly appRules: UnwrapRef<typeof import('./src/utils/rules')['appRules']>
    readonly axios: UnwrapRef<typeof import('axios')['default']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createFormData: UnwrapRef<typeof import('./src/utils/helper')['createFormData']>
    readonly createI18n: UnwrapRef<typeof import('vue-i18n')['createI18n']>
    readonly createPinia: UnwrapRef<typeof import('pinia')['createPinia']>
    readonly createRouter: UnwrapRef<typeof import('vue-router')['createRouter']>
    readonly createVuetify: UnwrapRef<typeof import('vuetify')['createVuetify']>
    readonly createWebHistory: UnwrapRef<typeof import('vue-router')['createWebHistory']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineStore: UnwrapRef<typeof import('pinia')['defineStore']>
    readonly dragHide: UnwrapRef<typeof import('./src/utils/helper')['dragHide']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly escapeUrl: UnwrapRef<typeof import('./src/utils/helper')['escapeUrl']>
    readonly fetchLocales: UnwrapRef<typeof import('./src/utils/helper')['fetchLocales']>
    readonly formatCounter: UnwrapRef<typeof import('./src/utils/helper')['formatCounter']>
    readonly formatDate: UnwrapRef<typeof import('./src/utils/helper')['formatDate']>
    readonly formatDecimal: UnwrapRef<typeof import('./src/utils/helper')['formatDecimal']>
    readonly formatFraction: UnwrapRef<typeof import('./src/utils/helper')['formatFraction']>
    readonly formatMS: UnwrapRef<typeof import('./src/utils/helper')['formatMS']>
    readonly formatMoney: UnwrapRef<typeof import('./src/utils/helper')['formatMoney']>
    readonly formatOklch: UnwrapRef<typeof import('./src/utils/helper')['formatOklch']>
    readonly formatPadding: UnwrapRef<typeof import('./src/utils/helper')['formatPadding']>
    readonly formatSize: UnwrapRef<typeof import('./src/utils/helper')['formatSize']>
    readonly formatSlug: UnwrapRef<typeof import('./src/utils/helper')['formatSlug']>
    readonly formatString: UnwrapRef<typeof import('./src/utils/helper')['formatString']>
    readonly generateRandomGuid: UnwrapRef<typeof import('./src/utils/helper')['generateRandomGuid']>
    readonly generateRandomString: UnwrapRef<typeof import('./src/utils/helper')['generateRandomString']>
    readonly getActivePinia: UnwrapRef<typeof import('pinia')['getActivePinia']>
    readonly getComponent: UnwrapRef<typeof import('./src/utils/helper')['getComponent']>
    readonly getComponentAsync: UnwrapRef<typeof import('./src/utils/helper')['getComponentAsync']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getCurrentWatcher: UnwrapRef<typeof import('vue')['getCurrentWatcher']>
    readonly getFileExtension: UnwrapRef<typeof import('./src/utils/helper')['getFileExtension']>
    readonly getProvider: UnwrapRef<typeof import('./src/utils/helper')['getProvider']>
    readonly getUserLocale: UnwrapRef<typeof import('./src/utils/helper')['getUserLocale']>
    readonly getUserTheme: UnwrapRef<typeof import('./src/utils/helper')['getUserTheme']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly i18n: UnwrapRef<typeof import('./src/utils/i18n')['i18n']>
    readonly imageFormData: UnwrapRef<typeof import('./src/utils/helper')['imageFormData']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly inputFilter: UnwrapRef<typeof import('./src/utils/helper')['inputFilter']>
    readonly inputRegex: UnwrapRef<typeof import('./src/utils/helper')['inputRegex']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly loadLocales: UnwrapRef<typeof import('./src/utils/helper')['loadLocales']>
    readonly loadMenu: UnwrapRef<typeof import('./src/utils/helper')['loadMenu']>
    readonly loadRoutes: UnwrapRef<typeof import('./src/utils/helper')['loadRoutes']>
    readonly lowerCase: UnwrapRef<typeof import('./src/utils/helper')['lowerCase']>
    readonly mapActions: UnwrapRef<typeof import('pinia')['mapActions']>
    readonly mapGetters: UnwrapRef<typeof import('pinia')['mapGetters']>
    readonly mapState: UnwrapRef<typeof import('pinia')['mapState']>
    readonly mapStores: UnwrapRef<typeof import('pinia')['mapStores']>
    readonly mapWritableState: UnwrapRef<typeof import('pinia')['mapWritableState']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mat: UnwrapRef<typeof import('./src/utils/icons-material')['mat']>
    readonly matAliases: UnwrapRef<typeof import('./src/utils/icons-material')['matAliases']>
    readonly mergeProps: UnwrapRef<typeof import('vue')['mergeProps']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly persistedstate: UnwrapRef<typeof import('pinia-plugin-persistedstate')['default']>
    readonly pho: UnwrapRef<typeof import('./src/utils/icons-phosphor')['pho']>
    readonly phoAliases: UnwrapRef<typeof import('./src/utils/icons-phosphor')['phoAliases']>
    readonly pinia: UnwrapRef<typeof import('./src/utils/pinia')['pinia']>
    readonly position: UnwrapRef<typeof import('./src/utils/helper')['position']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly query: UnwrapRef<typeof import('./src/utils/query')['query']>
    readonly queryData: UnwrapRef<typeof import('./src/utils/helper')['queryData']>
    readonly queryOptions: UnwrapRef<typeof import('./src/utils/query')['queryOptions']>
    readonly queryPrefetch: UnwrapRef<typeof import('./src/utils/helper')['queryPrefetch']>
    readonly queryWrapper: UnwrapRef<typeof import('./src/utils/helper')['queryWrapper']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly registerDirectives: UnwrapRef<typeof import('./src/utils/directive')['registerDirectives']>
    readonly registerI18n: UnwrapRef<typeof import('./src/utils/i18n')['registerI18n']>
    readonly registerMenu: UnwrapRef<typeof import('./src/utils/menu')['registerMenu']>
    readonly registerProviders: UnwrapRef<typeof import('./src/utils/provider')['registerProviders']>
    readonly registerRoutes: UnwrapRef<typeof import('./src/utils/router')['registerRoutes']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly router: UnwrapRef<typeof import('./src/utils/router')['router']>
    readonly setActivePinia: UnwrapRef<typeof import('pinia')['setActivePinia']>
    readonly setInitialData: UnwrapRef<typeof import('./src/utils/helper')['setInitialData']>
    readonly setMapStoreSuffix: UnwrapRef<typeof import('pinia')['setMapStoreSuffix']>
    readonly setProvider: UnwrapRef<typeof import('./src/utils/helper')['setProvider']>
    readonly setUserLocale: UnwrapRef<typeof import('./src/utils/helper')['setUserLocale']>
    readonly setUserTheme: UnwrapRef<typeof import('./src/utils/helper')['setUserTheme']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly storeToRefs: UnwrapRef<typeof import('pinia')['storeToRefs']>
    readonly tabler: UnwrapRef<typeof import('./src/utils/icons-tabler')['tabler']>
    readonly tablerAliases: UnwrapRef<typeof import('./src/utils/icons-tabler')['tablerAliases']>
    readonly tailwind3: UnwrapRef<typeof import('./src/utils/tailwind3')['default']>
    readonly timerAttempt: UnwrapRef<typeof import('./src/utils/helper')['timerAttempt']>
    readonly timerDebounce: UnwrapRef<typeof import('./src/utils/helper')['timerDebounce']>
    readonly timerSleep: UnwrapRef<typeof import('./src/utils/helper')['timerSleep']>
    readonly timerThrottle: UnwrapRef<typeof import('./src/utils/helper')['timerThrottle']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly ucFirst: UnwrapRef<typeof import('./src/utils/helper')['ucFirst']>
    readonly ucWords: UnwrapRef<typeof import('./src/utils/helper')['ucWords']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly upperCase: UnwrapRef<typeof import('./src/utils/helper')['upperCase']>
    readonly useAppStore: UnwrapRef<typeof import('./src/stores/appStore')['useAppStore']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useAuthStore: UnwrapRef<typeof import('./src/stores/authStore')['useAuthStore']>
    readonly useConfirmStore: UnwrapRef<typeof import('./src/stores/confirmStore')['useConfirmStore']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useI18n: UnwrapRef<typeof import('vue-i18n')['useI18n']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useMutation: UnwrapRef<typeof import('@tanstack/vue-query')['useMutation']>
    readonly useNotifyStore: UnwrapRef<typeof import('./src/stores/notifyStore')['useNotifyStore']>
    readonly usePromptStore: UnwrapRef<typeof import('./src/stores/promptStore')['usePromptStore']>
    readonly useQuery: UnwrapRef<typeof import('@tanstack/vue-query')['useQuery']>
    readonly useQueryClient: UnwrapRef<typeof import('@tanstack/vue-query')['useQueryClient']>
    readonly useReactiveStore: UnwrapRef<typeof import('./src/stores/reactiveStore')['useReactiveStore']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useSnackbarStore: UnwrapRef<typeof import('./src/stores/messageStore')['useSnackbarStore']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useTheme: UnwrapRef<typeof import('vuetify')['useTheme']>
    readonly useTranslate: UnwrapRef<typeof import('./src/utils/helper')['useTranslate']>
    readonly vuetify: UnwrapRef<typeof import('./src/utils/vuetify')['vuetify']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}