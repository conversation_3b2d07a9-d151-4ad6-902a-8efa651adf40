import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Sale, SaleCreate, SaleFilter, SaleStatistics } from '../types/sale'
import saleService from '../services/saleService'

export const useSaleStore = defineStore('sale', () => {
  // State
  const sales = ref<Sale[]>([])
  const currentSale = ref<Sale | null>(null)
  const statistics = ref<SaleStatistics | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filter = ref<SaleFilter>({
    paymentStatus: 'all',
    paymentMethod: 'all',
    sortBy: 'date',
    sortOrder: 'desc'
  })

  // Getters
  const filteredSales = computed(() => {
    let result = [...sales.value]
    
    // Müşteri ID filtresi
    if (filter.value.customerId) {
      result = result.filter(sale => sale.customerId === filter.value.customerId)
    }
    
    // <PERSON><PERSON>h aralığı filtresi
    if (filter.value.startDate) {
      const startDate = new Date(filter.value.startDate).getTime()
      result = result.filter(sale => new Date(sale.saleDate).getTime() >= startDate)
    }
    
    if (filter.value.endDate) {
      const endDate = new Date(filter.value.endDate).getTime()
      result = result.filter(sale => new Date(sale.saleDate).getTime() <= endDate)
    }
    
    // Ödeme durumu filtresi
    if (filter.value.paymentStatus && filter.value.paymentStatus !== 'all') {
      result = result.filter(sale => sale.paymentStatus === filter.value.paymentStatus)
    }
    
    // Ödeme yöntemi filtresi
    if (filter.value.paymentMethod && filter.value.paymentMethod !== 'all') {
      result = result.filter(sale => sale.paymentMethod === filter.value.paymentMethod)
    }
    
    // Tutar aralığı filtresi
    if (filter.value.minAmount !== undefined) {
      result = result.filter(sale => sale.finalAmount >= filter.value.minAmount!)
    }
    
    if (filter.value.maxAmount !== undefined) {
      result = result.filter(sale => sale.finalAmount <= filter.value.maxAmount!)
    }
    
    // Sıralama
    if (filter.value.sortBy) {
      result.sort((a, b) => {
        let valueA, valueB
        
        if (filter.value.sortBy === 'date') {
          valueA = new Date(a.saleDate).getTime()
          valueB = new Date(b.saleDate).getTime()
        } else if (filter.value.sortBy === 'amount') {
          valueA = a.finalAmount
          valueB = b.finalAmount
        } else {
          return 0
        }
        
        const direction = filter.value.sortOrder === 'desc' ? -1 : 1
        
        if (valueA < valueB) return -1 * direction
        if (valueA > valueB) return 1 * direction
        return 0
      })
    }
    
    return result
  })

  // Actions
  const fetchSales = async () => {
    loading.value = true
    error.value = null
    
    try {
      sales.value = await saleService.getAllSales()
    } catch (err) {
      error.value = 'Satışlar yüklenirken bir hata oluştu.'
      console.error('Satış yükleme hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSaleById = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      currentSale.value = await saleService.getSaleById(id)
    } catch (err) {
      error.value = 'Satış bilgileri yüklenirken bir hata oluştu.'
      console.error('Satış detay hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSalesByCustomerId = async (customerId: string) => {
    loading.value = true
    error.value = null
    
    try {
      sales.value = await saleService.getSalesByCustomerId(customerId)
    } catch (err) {
      error.value = 'Müşteri satışları yüklenirken bir hata oluştu.'
      console.error('Müşteri satışları hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSalesByDateRange = async (startDate: string, endDate: string) => {
    loading.value = true
    error.value = null
    
    try {
      sales.value = await saleService.getSalesByDateRange(startDate, endDate)
    } catch (err) {
      error.value = 'Tarih aralığındaki satışlar yüklenirken bir hata oluştu.'
      console.error('Tarih aralığı satışları hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchSalesStatistics = async () => {
    loading.value = true
    error.value = null
    
    try {
      statistics.value = await saleService.getSalesStatistics()
    } catch (err) {
      error.value = 'Satış istatistikleri yüklenirken bir hata oluştu.'
      console.error('Satış istatistikleri hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const createSale = async (sale: SaleCreate) => {
    loading.value = true
    error.value = null
    
    try {
      const newSale = await saleService.createSale(sale)
      sales.value.push(newSale)
      return newSale
    } catch (err) {
      error.value = 'Satış oluşturulurken bir hata oluştu.'
      console.error('Satış oluşturma hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateSale = async (id: string, sale: Partial<Sale>) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedSale = await saleService.updateSale(id, sale)
      
      // Satış listesini güncelle
      const index = sales.value.findIndex(s => s.id === id)
      if (index !== -1) {
        sales.value[index] = updatedSale
      }
      
      // Eğer mevcut satış ise onu da güncelle
      if (currentSale.value && currentSale.value.id === id) {
        currentSale.value = updatedSale
      }
      
      return updatedSale
    } catch (err) {
      error.value = 'Satış güncellenirken bir hata oluştu.'
      console.error('Satış güncelleme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteSale = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      await saleService.deleteSale(id)
      
      // Satışı listeden kaldır
      sales.value = sales.value.filter(s => s.id !== id)
      
      // Eğer mevcut satış ise temizle
      if (currentSale.value && currentSale.value.id === id) {
        currentSale.value = null
      }
    } catch (err) {
      error.value = 'Satış silinirken bir hata oluştu.'
      console.error('Satış silme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilter = (newFilter: Partial<SaleFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }

  const resetFilter = () => {
    filter.value = {
      paymentStatus: 'all',
      paymentMethod: 'all',
      sortBy: 'date',
      sortOrder: 'desc'
    }
  }

  return {
    // State
    sales,
    currentSale,
    statistics,
    loading,
    error,
    filter,
    
    // Getters
    filteredSales,
    
    // Actions
    fetchSales,
    fetchSaleById,
    fetchSalesByCustomerId,
    fetchSalesByDateRange,
    fetchSalesStatistics,
    createSale,
    updateSale,
    deleteSale,
    setFilter,
    resetFilter
  }
})