<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const collapsed = ref(false)

const menuItems = [
  { name: 'dashboard', path: '/dashboard', icon: 'pi-chart-bar', label: 'Gösterge Paneli' },
  { name: 'sales', path: '/sales', icon: 'pi-shopping-cart', label: 'Satışlar' },
  { name: 'customers', path: '/customers', icon: 'pi-users', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'products', path: '/products', icon: 'pi-box', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'settings', path: '/settings', icon: 'pi-cog', label: 'Ayarlar' }
]

const isActive = (path: string) => {
  return route.path === path || route.path.startsWith(`${path}/`)
}

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const sidebarClass = computed(() => {
  return collapsed.value ? 'sidebar collapsed' : 'sidebar'
})
</script>

<template>
  <aside :class="sidebarClass">
    <div class="sidebar-header">
      <div class="logo-container">
        <img src="/vite.svg" alt="Setre Logo" class="logo" v-if="!collapsed" />
        <span class="logo-text" v-if="!collapsed">Setre</span>
        <img src="/vite.svg" alt="Setre Logo" class="logo-small" v-else />
      </div>
      <button class="collapse-btn" @click="toggleSidebar">
        <span class="pi" :class="collapsed ? 'pi-angle-right' : 'pi-angle-left'"></span>
      </button>
    </div>
    
    <nav class="sidebar-nav">
      <ul class="nav-list">
        <li v-for="item in menuItems" :key="item.path" class="nav-item">
          <router-link 
            :to="item.path" 
            class="nav-link" 
            :class="{ active: isActive(item.path) }"
          >
            <span class="pi" :class="`${item.icon}`"></span>
            <span class="nav-label" v-if="!collapsed">{{ item.label }}</span>
          </router-link>
        </li>
      </ul>
    </nav>
    
    <div class="sidebar-footer">
      <button class="help-btn">
        <span class="pi pi-question-circle"></span>
        <span v-if="!collapsed">Yardım</span>
      </button>
    </div>
  </aside>
</template>

<style scoped>
.sidebar {
  width: 250px;
  background-color: #1F2937;
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  height: 100vh;
  position: sticky;
  top: 0;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 30px;
  width: auto;
}

.logo-small {
  height: 30px;
  width: auto;
  margin: 0 auto;
}

.logo-text {
  font-weight: 700;
  font-size: 1.25rem;
  color: white;
}

.collapse-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #D1D5DB;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0.25rem;
  margin: 0 0.5rem;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

.nav-link .pi {
  font-size: 1.25rem;
  width: 24px;
  text-align: center;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.help-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.5rem;
  background: none;
  border: none;
  color: #D1D5DB;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.help-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 0.75rem 0;
}

.sidebar.collapsed .help-btn {
  justify-content: center;
}
</style>