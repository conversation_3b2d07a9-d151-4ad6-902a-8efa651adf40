<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(() => {
  // Ana sayfadan gösterge paneline yönlendir
  router.push('/dashboard')
})
</script>

<template>
  <div class="home-container">
    <div class="loading-spinner">
      <span class="pi pi-spin pi-spinner"></span>
      <p>Yönlendiriliyor...</p>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner .pi {
  font-size: 2rem;
  color: var(--primary-color);
}
</style>