<?php

declare(strict_types=1);

use System\Migration\Migration;

class customer extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS customer (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `name` <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
         `surname` VA<PERSON><PERSON><PERSON>(50) NOT NULL,
         `email` VARCHAR(150) DEFAULT NULL,
         `phone` VARCHAR(50) UNIQUE NOT NULL,
         `tckn` VARCHAR(50) DEFAULT NULL,
         `address` VARCHAR(50) DEFAULT NULL,
         `current_debit` DECIMAL(10,2) NOT NULL DEFAULT 0,
         `is_risky` INT NOT NULL DEFAULT 0,
         `is_active` INT NOT NULL DEFAULT 1,
         `notes` VARCHAR(255) NOT NULL DEFAULT '',
         {$this->defaults()}
      )");
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS customer");
   }
}
