<script setup lang="ts">
const currentYear = new Date().getFullYear()
</script>

<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <p class="copyright">&copy; {{ currentYear }} Setre - Satış Takip ve Müşteri Yönetimi</p>
        <div class="footer-links">
          <a href="#" class="footer-link">Gizlilik Politikası</a>
          <a href="#" class="footer-link">Kullanım Koşulları</a>
          <a href="#" class="footer-link">Yardım</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.app-footer {
  background-color: white;
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  font-size: 0.875rem;
  color: #6B7280;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-link {
  color: #6B7280;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--primary-color);
}

@media (max-width: 640px) {
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>