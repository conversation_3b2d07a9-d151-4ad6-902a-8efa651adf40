<?php

declare(strict_types=1);

return [
   'err_required'      => '%s field is required',
   'err_numeric'       => '%s field must be numeric',
   'err_email'         => 'E-mail is invalid',
   'err_min_len'       => '%s field\'s length must be min %s',
   'err_max_len'       => '%s field\'s length must be max %s',
   'err_exact_len'     => '%s field\'s length must be %s',
   'err_alpha'         => '%s field must be alpha format',
   'err_alpha_num'     => '%s field must be alphanumeric format',
   'err_alpha_dash'    => '%s field must be alphadash format',
   'err_alpha_space'   => '%s field must be alphaspace format',
   'err_integer'       => '%s field must be integer format',
   'err_boolean'       => '%s field must be boolean format',
   'err_float'         => '%s field must be float format',
   'err_valid_url'     => '%s field must be valid URL format',
   'err_valid_ip'      => '%s field must be valid IP format',
   'err_valid_ipv4'    => '%s field must be valid IPv4 format',
   'err_valid_ipv6'    => '%s field must be valid IPv6 format',
   'err_valid_cc'      => '%s field must be valid credit card number',
   'err_contains'      => '%s field must contain "%s"',
   'err_min_numeric'   => '%s field must be min "%s"',
   'err_max_numeric'   => '%s field must be max "%s"',
   'err_matches'       => '%s field doesn\'t match with %s field',
   'err_must_be_array' => '%s field must be an array'
];
