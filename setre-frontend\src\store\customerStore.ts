import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Customer, CustomerFilter } from '../types/customer'
import customerService from '../services/customerService'

export const useCustomerStore = defineStore('customer', () => {
  // State
  const customers = ref<Customer[]>([])
  const currentCustomer = ref<Customer | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filter = ref<CustomerFilter>({
    search: '',
    status: 'all',
    customerType: 'all',
    sortBy: 'name',
    sortOrder: 'asc'
  })

  // Getters
  const filteredCustomers = computed(() => {
    let result = [...customers.value]
    
    // Arama filtresi
    if (filter.value.search) {
      const searchTerm = filter.value.search.toLowerCase()
      result = result.filter(customer => 
        customer.firstName.toLowerCase().includes(searchTerm) ||
        customer.lastName.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm) ||
        customer.phone.includes(searchTerm)
      )
    }
    
    // Durum filtresi
    if (filter.value.status && filter.value.status !== 'all') {
      result = result.filter(customer => customer.status === filter.value.status)
    }
    
    // Müşteri tipi filtresi
    if (filter.value.customerType && filter.value.customerType !== 'all') {
      result = result.filter(customer => customer.customerType === filter.value.customerType)
    }
    
    // Sıralama
    if (filter.value.sortBy) {
      result.sort((a, b) => {
        let valueA, valueB
        
        if (filter.value.sortBy === 'name') {
          valueA = `${a.firstName} ${a.lastName}`.toLowerCase()
          valueB = `${b.firstName} ${b.lastName}`.toLowerCase()
        } else if (filter.value.sortBy === 'createdAt') {
          valueA = new Date(a.createdAt).getTime()
          valueB = new Date(b.createdAt).getTime()
        } else if (filter.value.sortBy === 'totalPurchases') {
          valueA = a.totalPurchases || 0
          valueB = b.totalPurchases || 0
        } else {
          return 0
        }
        
        const direction = filter.value.sortOrder === 'desc' ? -1 : 1
        
        if (valueA < valueB) return -1 * direction
        if (valueA > valueB) return 1 * direction
        return 0
      })
    }
    
    return result
  })

  // Actions
  const fetchCustomers = async () => {
    loading.value = true
    error.value = null
    
    try {
      customers.value = await customerService.getAllCustomers()
    } catch (err) {
      error.value = 'Müşteriler yüklenirken bir hata oluştu.'
      console.error('Müşteri yükleme hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchCustomerById = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      currentCustomer.value = await customerService.getCustomerById(id)
    } catch (err) {
      error.value = 'Müşteri bilgileri yüklenirken bir hata oluştu.'
      console.error('Müşteri detay hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const createCustomer = async (customer: Omit<Customer, 'id'>) => {
    loading.value = true
    error.value = null
    
    try {
      const newCustomer = await customerService.createCustomer(customer)
      customers.value.push(newCustomer)
      return newCustomer
    } catch (err) {
      error.value = 'Müşteri oluşturulurken bir hata oluştu.'
      console.error('Müşteri oluşturma hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateCustomer = async (id: string, customer: Partial<Customer>) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedCustomer = await customerService.updateCustomer(id, customer)
      
      // Müşteri listesini güncelle
      const index = customers.value.findIndex(c => c.id === id)
      if (index !== -1) {
        customers.value[index] = updatedCustomer
      }
      
      // Eğer mevcut müşteri ise onu da güncelle
      if (currentCustomer.value && currentCustomer.value.id === id) {
        currentCustomer.value = updatedCustomer
      }
      
      return updatedCustomer
    } catch (err) {
      error.value = 'Müşteri güncellenirken bir hata oluştu.'
      console.error('Müşteri güncelleme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteCustomer = async (id: string) => {
    loading.value = true
    error.value = null
    
    try {
      await customerService.deleteCustomer(id)
      
      // Müşteriyi listeden kaldır
      customers.value = customers.value.filter(c => c.id !== id)
      
      // Eğer mevcut müşteri ise temizle
      if (currentCustomer.value && currentCustomer.value.id === id) {
        currentCustomer.value = null
      }
    } catch (err) {
      error.value = 'Müşteri silinirken bir hata oluştu.'
      console.error('Müşteri silme hatası:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const searchCustomers = async (query: string) => {
    if (!query.trim()) {
      return fetchCustomers()
    }
    
    loading.value = true
    error.value = null
    
    try {
      customers.value = await customerService.searchCustomers(query)
    } catch (err) {
      error.value = 'Müşteri araması yapılırken bir hata oluştu.'
      console.error('Müşteri arama hatası:', err)
    } finally {
      loading.value = false
    }
  }

  const setFilter = (newFilter: Partial<CustomerFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }

  const resetFilter = () => {
    filter.value = {
      search: '',
      status: 'all',
      customerType: 'all',
      sortBy: 'name',
      sortOrder: 'asc'
    }
  }

  return {
    // State
    customers,
    currentCustomer,
    loading,
    error,
    filter,
    
    // Getters
    filteredCustomers,
    
    // Actions
    fetchCustomers,
    fetchCustomerById,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    searchCustomers,
    setFilter,
    resetFilter
  }
})