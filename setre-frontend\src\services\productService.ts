import { apiClient } from './api'
import type { Product } from '../types/product'

// Örnek ürün verileri
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Laptop',
    description: 'Yüksek performanslı dizüstü bilgisayar',
    sku: 'LPT-001',
    barcode: '8901234567890',
    price: 12000,
    costPrice: 9000,
    stockQuantity: 15,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 5,
    supplier: 'ABC Elektronik'
  },
  {
    id: '2',
    name: 'Akıllı Telefon',
    description: 'Son model akıllı telefon',
    sku: 'PHN-002',
    barcode: '8901234567891',
    price: 8000,
    costPrice: 6000,
    stockQuantity: 25,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 8,
    supplier: 'ABC Elektronik'
  },
  {
    id: '3',
    name: 'Bluetooth Kulaklık',
    description: 'Kablosuz bluetooth kulaklık',
    sku: 'AUD-003',
    barcode: '8901234567892',
    price: 1200,
    costPrice: 800,
    stockQuantity: 50,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 10,
    supplier: 'XYZ Ses Sistemleri'
  },
  {
    id: '4',
    name: 'Ofis Koltuğu',
    description: 'Ergonomik ofis koltuğu',
    sku: 'FRN-004',
    barcode: '8901234567893',
    price: 2500,
    costPrice: 1800,
    stockQuantity: 10,
    category: 'Mobilya',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 3,
    supplier: 'Mobilya A.Ş.'
  },
  {
    id: '5',
    name: 'Yazıcı',
    description: 'Lazer yazıcı',
    sku: 'PRN-005',
    barcode: '8901234567894',
    price: 3500,
    costPrice: 2800,
    stockQuantity: 8,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 2,
    supplier: 'ABC Elektronik'
  },
  {
    id: '6',
    name: 'Monitör',
    description: '27 inç 4K monitör',
    sku: 'MON-006',
    barcode: '8901234567895',
    price: 4500,
    costPrice: 3500,
    stockQuantity: 12,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 4,
    supplier: 'ABC Elektronik'
  },
  {
    id: '7',
    name: 'Klavye',
    description: 'Mekanik klavye',
    sku: 'KBD-007',
    barcode: '8901234567896',
    price: 800,
    costPrice: 500,
    stockQuantity: 30,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 5,
    supplier: 'XYZ Bilgisayar'
  },
  {
    id: '8',
    name: 'Mouse',
    description: 'Kablosuz mouse',
    sku: 'MOU-008',
    barcode: '8901234567897',
    price: 400,
    costPrice: 250,
    stockQuantity: 40,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 8,
    supplier: 'XYZ Bilgisayar'
  },
  {
    id: '9',
    name: 'Tablet',
    description: '10 inç tablet',
    sku: 'TAB-009',
    barcode: '8901234567898',
    price: 5000,
    costPrice: 3800,
    stockQuantity: 0,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 3,
    supplier: 'ABC Elektronik'
  },
  {
    id: '10',
    name: 'Harici Disk',
    description: '1TB harici disk',
    sku: 'HDD-010',
    barcode: '8901234567899',
    price: 1500,
    costPrice: 1100,
    stockQuantity: 20,
    category: 'Elektronik',
    imageUrl: 'https://via.placeholder.com/150',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    taxRate: 18,
    unit: 'Adet',
    minStockLevel: 5,
    supplier: 'XYZ Bilgisayar'
  }
]

const productService = {
  /**
   * Tüm ürünleri getirir
   */
  getAllProducts: async (): Promise<Product[]> => {
    return [...mockProducts]
  },

  /**
   * Belirli bir ürünü ID'ye göre getirir
   */
  getProductById: async (id: string): Promise<Product> => {
    const product = mockProducts.find(p => p.id === id)
    if (!product) {
      throw new Error('Ürün bulunamadı')
    }
    return { ...product }
  },

  /**
   * Yeni bir ürün oluşturur
   */
  createProduct: async (product: Omit<Product, 'id'>): Promise<Product> => {
    const newProduct: Product = {
      ...product,
      id: (mockProducts.length + 1).toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    mockProducts.push(newProduct)
    return { ...newProduct }
  },

  /**
   * Mevcut bir ürünü günceller
   */
  updateProduct: async (id: string, product: Partial<Product>): Promise<Product> => {
    const index = mockProducts.findIndex(p => p.id === id)
    if (index === -1) {
      throw new Error('Ürün bulunamadı')
    }
    
    const updatedProduct: Product = {
      ...mockProducts[index],
      ...product,
      updatedAt: new Date().toISOString()
    }
    
    mockProducts[index] = updatedProduct
    return { ...updatedProduct }
  },

  /**
   * Bir ürünü siler
   */
  deleteProduct: async (id: string): Promise<void> => {
    const index = mockProducts.findIndex(p => p.id === id)
    if (index === -1) {
      throw new Error('Ürün bulunamadı')
    }
    
    mockProducts.splice(index, 1)
  },

  /**
   * Ürünleri arar
   */
  searchProducts: async (query: string): Promise<Product[]> => {
    if (!query.trim()) {
      return [...mockProducts]
    }
    
    const searchLower = query.toLowerCase()
    return mockProducts.filter(product => {
      return (
        product.name.toLowerCase().includes(searchLower) ||
        (product.description && product.description.toLowerCase().includes(searchLower)) ||
        product.sku.toLowerCase().includes(searchLower) ||
        (product.barcode && product.barcode.toLowerCase().includes(searchLower)) ||
        (product.category && product.category.toLowerCase().includes(searchLower))
      )
    })
  },

  /**
   * Stok durumuna göre ürünleri filtreler
   */
  getProductsByStockStatus: async (inStock: boolean): Promise<Product[]> => {
    return mockProducts.filter(product => {
      return inStock ? product.stockQuantity > 0 : product.stockQuantity <= 0
    })
  }
}

export default productService