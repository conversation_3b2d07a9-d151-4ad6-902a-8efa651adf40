import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/Home.vue'),
    meta: { title: 'Ana Sayfa' }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { title: 'Gösterge Paneli' }
  },
  {
    path: '/sales',
    name: 'sales',
    component: () => import('../views/Sales.vue'),
    meta: { title: 'Satışlar' }
  },
  {
    path: '/sales/new',
    name: 'new-sale',
    component: () => import('../views/SaleDetail.vue'),
    meta: { title: '<PERSON><PERSON>ı<PERSON>' },
    props: { isNew: true }
  },
  {
    path: '/sales/:id',
    name: 'sale-details',
    component: () => import('../views/SaleDetail.vue'),
    meta: { title: '<PERSON><PERSON><PERSON><PERSON>ayları' },
    props: true
  },
  {
    path: '/customers',
    name: 'customers',
    component: () => import('../views/Customers.vue'),
    meta: { title: 'Müşteriler' }
  },
  {
    path: '/customers/new',
    name: 'new-customer',
    component: () => import('../views/CustomerDetail.vue'),
    meta: { title: 'Yeni Müşteri' },
    props: { isNew: true }
  },
  {
    path: '/customers/:id',
    name: 'customer-details',
    component: () => import('../views/CustomerDetail.vue'),
    meta: { title: 'Müşteri Detayları' },
    props: true
  },
  {
    path: '/products',
    name: 'products',
    component: () => import('../views/Products.vue'),
    meta: { title: 'Ürünler' }
  },
  {
    path: '/products/new',
    name: 'new-product',
    component: () => import('../views/ProductDetail.vue'),
    meta: { title: 'Yeni Ürün' },
    props: { isNew: true }
  },
  {
    path: '/products/:id',
    name: 'product-details',
    component: () => import('../views/ProductDetail.vue'),
    meta: { title: 'Ürün Detayları' },
    props: true
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('../views/Settings.vue'),
    meta: { title: 'Ayarlar' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFound.vue'),
    meta: { title: 'Sayfa Bulunamadı' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title || 'Setre'} - Satış Takip ve Müşteri Yönetimi`
  next()
})

export default router