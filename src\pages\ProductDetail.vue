<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="product.content"
                     v-bind:rules="[appRules.required()]"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="product.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ product.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image", 2) }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="product.image_list" />
                  <ImageUpload
                     v-model="imageUpload"
                     multiple />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <Card>
         <template v-slot:extension>
            <v-card-title class="text-base">Bağlantılar</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="product.category_list"
                     v-bind:items="categoryAll"
                     v-bind:loading="categoryLoading"
                     item-value="id"
                     multiple
                     return-object />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { appRules } from "@/utils/rules";
import { useGetCategoryAll } from "../services/CategoryService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";
import { IProduct, IProductStore, useCreateProduct, useGetProductById, useUpdateProduct } from "../services/ProductService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// product
const product = ref({
   is_active: 1,
   sort_order: 1,
   category_list: [] as any
} as IProduct);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("app.createProduct") : t("app.productDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("ProductDetail", title);

// services
const getProductById = useGetProductById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (item) => {
      product.value = { ...item };
   }
});
const updateProduct = useUpdateProduct();
const createProduct = useCreateProduct();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["product", "productById"] });

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();

// loading
const isLoading = computed(() => getProductById.isLoading.value);
const isPending = computed(() => createProduct.isPending.value || updateProduct.isPending.value);
const isError = computed(() => getProductById.isError.value);

// handlers
const deleteImageHandler = async (image: any) => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteImage.mutateAsync({
            id: image.id,
            path: image.image_path,
            table: "product_image",
            method: "delete",
            unlink: false
         });
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: IProductStore = {
      code: product.value.code,
      title: product.value.title,
      content: product.value.content,
      is_active: product.value.is_active,
      sort_order: product.value.sort_order,
      product_category: product.value.category_list.map((item) => item.id)
   };

   try {
      if (imageUpload.value.length) {
         const upload = await uploadImage.mutateAsync({
            files: imageUpload.value,
            path: "product/" + product.value.id
         });
         payload.image_path = upload.data;

         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createProduct.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateProduct.mutateAsync({ id: product.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
</script>
