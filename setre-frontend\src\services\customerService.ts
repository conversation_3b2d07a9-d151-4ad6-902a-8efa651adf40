import { apiClient } from './api'
import type { Customer } from '../types/customer'

/**
 * JSONPlaceholder API'den gelen kullanıcı verisini Customer tipine dönüştürür
 */
const transformToCustomer = (user: any): Customer => {
  return {
    id: user.id.toString(),
    firstName: user.name.split(' ')[0],
    lastName: user.name.split(' ').slice(1).join(' '),
    email: user.email,
    phone: user.phone,
    address: user.address?.street,
    city: user.address?.city,
    postalCode: user.address?.zipcode,
    country: 'Türkiye', // Varsayılan değer
    notes: user.company?.catchPhrase,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    totalPurchases: 0, // Varsayılan değer
    lastPurchaseDate: null,
    customerType: 'regular',
    status: 'active'
  }
}

/**
 * Customer verisini API formatına dönüştürür
 */
const customerToApiFormat = (customer: Partial<Customer>): any => {
  // Eğer tam bir isim var<PERSON> kull<PERSON>, yoksa firstName ve lastName'i birleştir
  const name = customer.firstName && customer.lastName 
    ? `${customer.firstName} ${customer.lastName}`
    : undefined

  return {
    name,
    email: customer.email,
    phone: customer.phone,
    address: customer.address ? {
      street: customer.address,
      city: customer.city,
      zipcode: customer.postalCode
    } : undefined,
    company: customer.notes ? {
      catchPhrase: customer.notes
    } : undefined
  }
}

const customerService = {
  /**
   * Tüm müşterileri getirir
   */
  getAllCustomers: async (): Promise<Customer[]> => {
    const response = await apiClient.get('/users')
    return response.data.map((user: any) => transformToCustomer(user))
  },

  /**
   * Belirli bir müşteriyi ID'ye göre getirir
   */
  getCustomerById: async (id: string): Promise<Customer> => {
    const response = await apiClient.get(`/users/${id}`)
    return transformToCustomer(response.data)
  },

  /**
   * Yeni bir müşteri oluşturur
   */
  createCustomer: async (customer: Omit<Customer, 'id'>): Promise<Customer> => {
    const response = await apiClient.post('/users', customerToApiFormat(customer))
    return transformToCustomer(response.data)
  },

  /**
   * Mevcut bir müşteriyi günceller
   */
  updateCustomer: async (id: string, customer: Partial<Customer>): Promise<Customer> => {
    const response = await apiClient.put(`/users/${id}`, customerToApiFormat(customer))
    return transformToCustomer(response.data)
  },

  /**
   * Bir müşteriyi siler
   */
  deleteCustomer: async (id: string): Promise<void> => {
    await apiClient.delete(`/users/${id}`)
  },

  /**
   * Müşterileri arar
   */
  searchCustomers: async (query: string): Promise<Customer[]> => {
    // JSONPlaceholder API'si arama desteği sunmadığı için tüm kullanıcıları alıp istemci tarafında filtreliyoruz
    const response = await apiClient.get('/users')
    const customers = response.data.map((user: any) => transformToCustomer(user))
    
    // İsim, soyisim veya e-posta içinde arama yap
    return customers.filter((customer: Customer) => {
      const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase()
      const searchLower = query.toLowerCase()
      return fullName.includes(searchLower) || 
             (customer.email && customer.email.toLowerCase().includes(searchLower))
    })
  }
}

export default customerService