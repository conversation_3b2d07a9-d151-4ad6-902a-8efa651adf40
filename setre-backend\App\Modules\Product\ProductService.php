<?php

declare(strict_types=1);

namespace App\Modules\Product;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use App\Modules\Product\ProductRequest;
use System\Exception\SystemException;
use App\Modules\Product\ProductRepository;

class ProductService extends BaseService {
   /** @var ProductRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      ProductRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllProduct(): array {
      $result = $this->repository->findAll();
      return $result;
   }

   public function getProduct(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createProduct(ProductRequest $dto): array {
      $existingCode = $this->repository->findBy(['code' => $dto->code]);
      $existingTitle = $this->repository->findBy(['title' => $dto->title]);

      if (!empty($existingCode)) {
         throw new SystemException('Product code already exists', 400);
      }
      if (!empty($existingTitle)) {
         throw new SystemException('Product title already exists', 400);
      }
      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'code' => 'required',
            'title' => 'required',
            'price' => 'required|numeric',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric'
         ]);

         $id = $this->create([
            'code' => $dto->code,
            'title' => $dto->title,
            'content' => $dto->content,
            'price' => $dto->price,
            'stock' => $dto->stock,
            'category_id' => $dto->category_id,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order
         ]);

         return $this->getProduct($id);
      });
   }

   public function updateProduct(ProductRequest $dto): array {
      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'code' => 'required',
            'title' => 'required',
            'price' => 'required|numeric',
            'is_active' => 'required|numeric',
            'sort_order' => 'required|numeric'
         ]);

         $this->update($dto, [
            'code' => $dto->code,
            'title' => $dto->title,
            'content' => $dto->content,
            'price' => $dto->price,
            'stock' => $dto->stock,
            'category_id' => $dto->category_id,
            'is_active' => $dto->is_active,
            'sort_order' => $dto->sort_order
         ], [
            'id' => $dto->id
         ]);

         return $this->getProduct($dto->id);
      });
   }

}
