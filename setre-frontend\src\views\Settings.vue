<script setup lang="ts">
import { ref } from 'vue'

// Ayarlar için state tanımlamaları
const generalSettings = ref({
  companyName: 'Setre',
  taxRate: 18,
  currency: 'TRY',
  language: 'tr'
})

const userSettings = ref({
  notifications: true,
  darkMode: false,
  autoSave: true
})

const success = ref('')
const error = ref('')

// Ayarları kaydet
const saveSettings = () => {
  try {
    // Burada ayarları kaydetme işlemi yapılacak
    // Örnek olarak localStorage'a kaydediyoruz
    localStorage.setItem('generalSettings', JSON.stringify(generalSettings.value))
    localStorage.setItem('userSettings', JSON.stringify(userSettings.value))
    
    success.value = 'Ayarlar başarıyla kaydedildi'
    setTimeout(() => {
      success.value = ''
    }, 3000)
  } catch (err) {
    error.value = '<PERSON>yar<PERSON> kaydedilirken bir hata oluştu'
    console.error(err)
  }
}

// Ayarları sıfırla
const resetSettings = () => {
  generalSettings.value = {
    companyName: 'Setre',
    taxRate: 18,
    currency: 'TRY',
    language: 'tr'
  }
  
  userSettings.value = {
    notifications: true,
    darkMode: false,
    autoSave: true
  }
}
</script>

<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>Ayarlar</h1>
    </div>
    
    <div v-if="success" class="success-message">
      <span class="pi pi-check-circle"></span>
      <p>{{ success }}</p>
    </div>
    
    <div v-if="error" class="error-message">
      <span class="pi pi-exclamation-triangle"></span>
      <p>{{ error }}</p>
    </div>
    
    <div class="settings-content">
      <!-- Genel Ayarlar -->
      <div class="card">
        <h2>Genel Ayarlar</h2>
        
        <div class="form-group">
          <label for="companyName">Şirket Adı</label>
          <input 
            id="companyName" 
            type="text" 
            class="form-control" 
            v-model="generalSettings.companyName"
          />
        </div>
        
        <div class="form-group">
          <label for="taxRate">Varsayılan KDV Oranı (%)</label>
          <input 
            id="taxRate" 
            type="number" 
            class="form-control" 
            v-model.number="generalSettings.taxRate"
            min="0"
            max="100"
          />
        </div>
        
        <div class="form-group">
          <label for="currency">Para Birimi</label>
          <select 
            id="currency" 
            class="form-control" 
            v-model="generalSettings.currency"
          >
            <option value="TRY">Türk Lirası (₺)</option>
            <option value="USD">Amerikan Doları ($)</option>
            <option value="EUR">Euro (€)</option>
            <option value="GBP">İngiliz Sterlini (£)</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="language">Dil</label>
          <select 
            id="language" 
            class="form-control" 
            v-model="generalSettings.language"
          >
            <option value="tr">Türkçe</option>
            <option value="en">İngilizce</option>
          </select>
        </div>
      </div>
      
      <!-- Kullanıcı Ayarları -->
      <div class="card">
        <h2>Kullanıcı Ayarları</h2>
        
        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="userSettings.notifications"
            />
            Bildirimleri Etkinleştir
          </label>
        </div>
        
        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="userSettings.darkMode"
            />
            Karanlık Mod
          </label>
        </div>
        
        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="userSettings.autoSave"
            />
            Otomatik Kaydetme
          </label>
        </div>
      </div>
      
      <!-- Eylemler -->
      <div class="settings-actions">
        <button class="btn" @click="resetSettings">
          <span class="pi pi-refresh"></span>
          Varsayılana Sıfırla
        </button>
        <button class="btn btn-primary" @click="saveSettings">
          <span class="pi pi-save"></span>
          Ayarları Kaydet
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.settings-container {
  width: 100%;
}

.settings-header {
  margin-bottom: 1.5rem;
}

.settings-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 0.25rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

.checkbox-group {
  margin-bottom: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1rem;
}

.success-message,
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

@media (max-width: 768px) {
  .settings-actions {
    flex-direction: column;
  }
  
  .settings-actions .btn {
    width: 100%;
  }
}
</style>