import type { Customer } from './customer'
import type { Product } from './product'

export interface SaleItem {
  id: string
  productId: string
  product?: Product
  quantity: number
  unitPrice: number
  discount?: number
  total: number
}

export interface Sale {
  id: string
  customerId: string
  customer?: Customer
  items: SaleItem[]
  totalAmount: number
  discount?: number
  tax?: number
  finalAmount: number
  paymentMethod: 'cash' | 'credit_card' | 'bank_transfer' | 'other'
  paymentStatus: 'paid' | 'pending' | 'cancelled'
  notes?: string
  saleDate: string
  createdAt: string
  updatedAt: string
}

export type SaleCreate = Omit<Sale, 'id' | 'createdAt' | 'updatedAt'>

export interface SaleFilter {
  customerId?: string
  startDate?: string
  endDate?: string
  paymentStatus?: 'paid' | 'pending' | 'cancelled' | 'all'
  paymentMethod?: 'cash' | 'credit_card' | 'bank_transfer' | 'other' | 'all'
  minAmount?: number
  maxAmount?: number
  sortBy?: 'date' | 'amount'
  sortOrder?: 'asc' | 'desc'
}

export interface SaleStatistics {
  totalSales: number
  totalRevenue: number
  averageSaleValue: number
  salesByPaymentMethod: Record<string, number>
  salesByDate: Record<string, number>
  topSellingProducts: Array<{ productId: string, productName: string, quantity: number, revenue: number }>
  topCustomers: Array<{ customerId: string, customerName: string, purchases: number, spent: number }>
}