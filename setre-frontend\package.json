{"name": "setre-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.6.0", "axios": "^1.11.0", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.7", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.1.0", "vue-tsc": "^3.0.5"}}