<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const searchQuery = ref('')

const handleSearch = () => {
  // Arama işlemi burada gerçekleştirilecek
  console.log('Arama yapılıyor:', searchQuery.value)
}
</script>

<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <div class="search-box">
          <span class="pi pi-search search-icon"></span>
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder="Ara..." 
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="header-actions">
          <button class="btn-icon" title="Bildirimler">
            <span class="pi pi-bell"></span>
            <span class="notification-badge">3</span>
          </button>
          <div class="user-menu">
            <img src="https://randomuser.me/api/portraits/men/1.jpg" alt="Kullanıcı" class="user-avatar" />
            <span class="user-name"><PERSON>llanı<PERSON>ı Adı</span>
            <span class="pi pi-chevron-down"></span>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
.app-header {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-box {
  position: relative;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
}

.search-box input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background-color: #F3F4F6;
}

.btn-icon .pi {
  font-size: 1.25rem;
  color: #4B5563;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--error-color);
  color: white;
  font-size: 0.75rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.user-menu:hover {
  background-color: #F3F4F6;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
}
</style>