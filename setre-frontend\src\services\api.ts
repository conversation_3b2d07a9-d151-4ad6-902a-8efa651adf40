import axios from 'axios'

// API istemcisi oluşturma - JSONPlaceholder için
const apiClient = axios.create({
  baseURL: 'https://jsonplaceholder.typicode.com', // JSONPlaceholder API
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
})

// MockAPI.io için müşteri istemcisi
const customerApiClient = axios.create({
  baseURL: 'https://mockapi.io/api/v1', // MockAPI.io Customers API
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
})

// MockAPI.io için satış istemcisi
const saleApiClient = axios.create({
  baseURL: 'https://mockapi.io/api/v1', // MockAPI.io Sales API
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
})

// İstek interceptor'ları
apiClient.interceptors.request.use(
  config => {
    // İstek gönderilmeden önce yapılacak işlemler
    // Örneğin: Token ekleme
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Yanıt interceptor'ları
apiClient.interceptors.response.use(
  response => {
    // Başarılı yanıtlar için işlemler
    return response
  },
  error => {
    // Hata yanıtları için işlemler
    if (error.response) {
      // Sunucu yanıtı ile dönen hatalar (4xx, 5xx)
      console.error('API Hatası:', error.response.data)
      
      // 401 Unauthorized hatası durumunda oturum sonlandırma
      if (error.response.status === 401) {
        // localStorage.removeItem('token')
        // window.location.href = '/login'
      }
    } else if (error.request) {
      // İstek yapıldı ancak yanıt alınamadı
      console.error('Ağ Hatası:', error.request)
    } else {
      // İstek oluşturulurken bir hata oluştu
      console.error('İstek Hatası:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// Customer API için interceptor'lar
customerApiClient.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

customerApiClient.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('Customer API Hatası:', error)
    return Promise.reject(error)
  }
)

// Sale API için interceptor'lar
saleApiClient.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

saleApiClient.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('Sale API Hatası:', error)
    return Promise.reject(error)
  }
)

export { apiClient, customerApiClient, saleApiClient }