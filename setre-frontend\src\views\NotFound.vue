<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Sayfa bulunamadı durumunda 3 saniye sonra gösterge paneline yönlendir
onMounted(() => {
  setTimeout(() => {
    router.push('/dashboard')
  }, 3000)
})
</script>

<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <h1>Sayfa Bulunamadı</h1>
      <p>Aradığınız sayfa mevcut değil veya taşınmış olabilir.</p>
      <p class="redirect-message">Birkaç saniye içinde gösterge paneline yönlendirileceksiniz...</p>
      <button class="btn btn-primary" @click="router.push('/dashboard')">
        <span class="pi pi-home"></span>
        Gösterge Paneline Dön
      </button>
    </div>
  </div>
</template>

<style scoped>
.not-found-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.error-code {
  font-size: 6rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}

p {
  color: #6B7280;
  margin-bottom: 1.5rem;
}

.redirect-message {
  font-style: italic;
  font-size: 0.875rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}
</style>