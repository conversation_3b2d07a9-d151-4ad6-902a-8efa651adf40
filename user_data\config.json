{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1000, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["ETH/USDT", "BTC/USDT", "XRP/USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList", "number_assets": 20, "sort_key": "quoteVolume", "min_value": 0, "refresh_period": 1800}], "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "1276691157"}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "79d70423e648c3955221f28f1eca60e08e1fba0d7e521581f1f6dc4f9b71f4b7", "ws_token": "2v_BXMefcrUcEs72uSbXEhM-Y--CEQOOvw", "CORS_origins": [], "username": "fq", "password": "fq"}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}