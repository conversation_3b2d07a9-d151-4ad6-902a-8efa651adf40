# --- Revize Edilmiş ve Geliştirilmiş Strateji v2.5 ---
# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime
from pandas import DataFrame
from typing import Optional, Dict, Any

from freqtrade.strategy import (
    IStrategy,
    Trade,
    BooleanParameter,
    DecimalParameter,
    IntParameter,
    merge_informative_pair,
)

import talib.abstract as ta
from technical import qtpylib

# --- KULLANICI TANIMLI FONKSİYONLAR ---
def safediv(numerator, denominator, default=0.0):
    """<PERSON><PERSON><PERSON><PERSON><PERSON> bö<PERSON>, NaN ve sonsuz değerleri güvenli bir şekilde işler"""
    # Denominator'da sorunlu değerleri kontrol et
    valid_mask = (denominator != 0) & (~pd.isna(denominator)) & (~np.isinf(denominator))

    # <PERSON><PERSON>u hesapla
    result = pd.Series(default, index=numerator.index)
    result[valid_mask] = numerator[valid_mask] / denominator[valid_mask]

    return result

def supertrend(dataframe: DataFrame, period: int = 10, multiplier: float = 3) -> Dict[str, Any]:
    """SuperTrend indikatörünü hesaplar"""
    df = dataframe.copy()

    # ATR hesapla
    df['h-l'] = df['high'] - df['low']
    df['h-pc'] = abs(df['high'] - df['close'].shift(1))
    df['l-pc'] = abs(df['low'] - df['close'].shift(1))
    df['tr'] = df[['h-l', 'h-pc', 'l-pc']].max(axis=1)
    df['atr'] = ta.EMA(df['tr'], timeperiod=period)

    # Temel hesaplamalar
    df['upperband'] = (df['high'] + df['low']) / 2 + multiplier * df['atr']
    df['lowerband'] = (df['high'] + df['low']) / 2 - multiplier * df['atr']

    # Supertrend hesapla
    df['supertrend'] = 0.0
    df['direction'] = 1  # 1: Yukarı trend, -1: Aşağı trend

    for i in range(period, len(df)):
        if df['close'].iat[i] > df['upperband'].iat[i-1]:
            df['direction'].iat[i] = 1
        elif df['close'].iat[i] < df['lowerband'].iat[i-1]:
            df['direction'].iat[i] = -1
        else:
            df['direction'].iat[i] = df['direction'].iat[i-1]

            # Trend devam ediyorsa bandı güncelle
            if df['direction'].iat[i-1] == 1:
                df['upperband'].iat[i] = min(df['upperband'].iat[i-1], df['upperband'].iat[i])
            else:
                df['lowerband'].iat[i] = max(df['lowerband'].iat[i-1], df['lowerband'].iat[i])

        # Son olarak supertrend değerini belirle
        if df['direction'].iat[i] == 1:
            df['supertrend'].iat[i] = df['lowerband'].iat[i]
        else:
            df['supertrend'].iat[i] = df['upperband'].iat[i]

    return {
        'supertrend': df['supertrend'],
        'direction': df['direction']
    }
# -------------------------------------

class AdvancedAlphaStrategyV2_4(IStrategy):
    """
    Advanced Multi-Factor Alpha Strategy v2.5 - Pandas uyumlu
    - safediv fonksiyonu pandas serileriyle uyumlu hale getirildi
    - Spot piyasada çalışabilmesi için gerekli düzenlemeler yapıldı
    """

    INTERFACE_VERSION = 3
    can_short: bool = False
    use_custom_stoploss = True

    minimal_roi = {
        "0": 0.02,    # %2 kar hedefi (5m timeframe için uygun)
        "15": 0.015,  # 15 dakika sonra %1.5
        "30": 0.01,   # 30 dakika sonra %1
        "60": 0.005   # 60 dakika sonra %0.5
    }

    stoploss = -0.99  # Sigorta stop-loss'u

    timeframe = '5m'
    informative_timeframe = '1h'
    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = True
    startup_candle_count: int = 200

    # Hiper-optimizasyon Parametreleri
    buy_rsi_threshold = IntParameter(20, 45, default=35, space="buy")
    sell_rsi_threshold = IntParameter(55, 80, default=65, space="sell")
    buy_alpha_score_threshold = DecimalParameter(0.1, 1.5, default=0.7, space="buy")
    buy_atr_multiplier = DecimalParameter(1.5, 4.0, default=2.5, space="buy")

    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        return [(pair, self.informative_timeframe) for pair in pairs]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        informative_df = self.dp.get_pair_dataframe(pair=metadata['pair'], timeframe=self.informative_timeframe)

        # 1h timeframe için temel göstergeler
        informative_df['ema_50'] = ta.EMA(informative_df, timeperiod=50)
        informative_df['ema_200'] = ta.EMA(informative_df, timeperiod=200)
        informative_df['adx'] = ta.ADX(informative_df, timeperiod=14)

        # Piyasa rejimi tespiti
        informative_df['regime'] = np.where(
            (informative_df['ema_50'] > informative_df['ema_200']) & (informative_df['adx'] > 25), 1,
            np.where((informative_df['ema_50'] < informative_df['ema_200']) & (informative_df['adx'] > 25), -1, 0)
        )

        # 5m timeframe ile birleştir
        dataframe = merge_informative_pair(dataframe, informative_df, self.timeframe, self.informative_timeframe, ffill=True)

        # 5m timeframe için göstergeler
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=12)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=26)
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # SuperTrend hesapla (kendi implementasyonumuz)
        st_result = supertrend(dataframe, period=10, multiplier=3)
        dataframe['supertrend_direction'] = st_result['direction']

        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # Hacim analizi
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        # Safediv yerine kendi fonksiyonumuzu kullanıyoruz
        dataframe['volume_ratio'] = safediv(dataframe['volume'], dataframe['volume_sma'], 1)

        # Alpha Score hesaplama
        rolling_window = 100

        def rolling_zscore(series):
            mean = series.rolling(rolling_window).mean()
            std = series.rolling(rolling_window).std()
            # Safediv yerine kendi fonksiyonumuzu kullanıyoruz
            return safediv(series - mean, std, 0)

        rsi_norm = rolling_zscore(dataframe['rsi'])
        volume_norm = rolling_zscore(dataframe['volume_ratio'])

        atr_percent = safediv(dataframe['atr'], dataframe['close'], 0)
        volatility_norm = rolling_zscore(atr_percent) * -1

        dataframe['alpha_score'] = rsi_norm + volume_norm + volatility_norm

        # DEBUG SÜTUNLARI - Backtest sonrası UI'da inceleyebilirsiniz
        dataframe['debug_alpha_score'] = dataframe['alpha_score']
        dataframe['debug_rsi'] = dataframe['rsi']
        dataframe['debug_supertrend_dir'] = dataframe['supertrend_direction']
        dataframe['debug_ema_fast'] = dataframe['ema_fast']
        dataframe['debug_ema_slow'] = dataframe['ema_slow']

        # Koşul analiz sütunu
        dataframe['debug_conditions_met'] = (
            (dataframe['regime_1h'].ffill() >= 0).astype(int) +
            (dataframe['supertrend_direction'] == 1).astype(int) +
            (dataframe['volume'] > 0).astype(int) +
            (dataframe['alpha_score'] > 0.3).astype(int) +
            (dataframe['rsi'] < 45).astype(int) +
            qtpylib.crossed_above(dataframe['ema_fast'], dataframe['ema_slow']).astype(int)
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Mevcut koşullar çok fazla pozisyon açıyor, bunları daha seçici hale getirelim
        long_conditions = (
            (dataframe['regime_1h'].ffill() == 1) &  # Sadece yükselen trend (1), yatay (0) değil
            (dataframe['supertrend_direction'] == 1) &
            (dataframe['volume_ratio'] > 1.2) &  # Normalden %20 fazla hacim
            (dataframe['alpha_score'] > 0.8) &  # Daha yüksek güven
            (dataframe['rsi'] < self.buy_rsi_threshold.value - 5) &  # Daha aşırı aşırı satım
            (qtpylib.crossed_above(dataframe['ema_fast'], dataframe['ema_slow']))
        )
        dataframe.loc[long_conditions, ['enter_long', 'enter_tag']] = (1, 'long_ema_cross')
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Mevcut çıkış koşulları çok geç kapanıyor
        long_exit = (
            # Daha hızlı kapanış için RSI kapanış koşulu
            (dataframe['rsi'] > self.sell_rsi_threshold.value - 5) |

            # SuperTrend yön değişimi
            (dataframe['supertrend_direction'] == -1) |

            # Hacim azalması
            (dataframe['volume_ratio'] < 0.8) |

            # Kayıp sınırı
            (dataframe['close'] < dataframe['open'] * 0.995)
        )
        dataframe.loc[long_exit, ['exit_long', 'exit_tag']] = (1, 'exit_long_signal')
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) < 1:
            return self.stoploss  # Veri yoksa varsayılan stop-loss

        latest_candle = dataframe.iloc[-1]

        atr_value = latest_candle.get('atr', 0.0)
        if atr_value <= 0.0:
            return self.stoploss  # Geçersiz ATR değeri

        # Stop fiyatını ATR kullanarak hesapla
        stop_price = atr_value * self.buy_atr_multiplier.value

        # Scalar değerlerle çalıştığımız için safediv yerine basit bölme kullan
        if current_rate != 0:
            stoploss_pct = stop_price / current_rate
        else:
            stoploss_pct = 0.05  # Varsayılan %5

        # Minimum %2 stop-loss koruması
        return -abs(max(0.02, stoploss_pct))

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                        proposed_stake: float, min_stake: float, max_stake: float,
                        entry_tag: Optional[str], side: str, **kwargs) -> float:
        """
        Pozisyon boyutunu Alpha Score'a göre dinamik ayarlar
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) < 1:
            return self.wallet_percent or 0.02  # Varsayılan stake

        latest = dataframe.iloc[-1]
        alpha_score = latest['alpha_score']

        # Alpha Score 0.8+ ise maksimum stake, 0.5-0.8 arasında orantılı stake
        if alpha_score >= 0.8:
            return max_stake * 0.33  # %33 stake
        elif alpha_score >= 0.6:
            return max_stake * 0.2  # %20 stake
        else:
            return 0  # Bu pozisyonu açma